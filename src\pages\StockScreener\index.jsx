import React, { useState, useEffect, useMemo } from "react";
import MainContentHeader from "../../components/common/MainContentHeader";
import MarketSelector from "../../components/common/MarketSelector";
import DataPanel from "./DataPanel";
import StockScreener from "./StockScreener";

const Index = () => {
  // Centralized state
  const [selectedMarketRaw, setSelectedMarketRaw] = useState(
    () => localStorage.getItem("selectedMarketRaw") || "all"
  );
  const [currentPage, setCurrentPage] = useState(1);
  const [activeTab, setActiveTab] = useState("overview");

  // States for filters from DataPanel
  const [marketCapCond, setMarketCapCond] = useState('Any');
  const [marketCapValue, setMarketCapValue] = useState(null);
  const [stockPriceCond, setStockPriceCond] = useState('Any');
  const [stockPriceValue, setStockPriceValue] = useState(null);
  const [industry, setIndustry] = useState('Any');

  // Handle market selection and persist
  const handleMarketChange = (newMarket) => {
    setSelectedMarketRaw(newMarket);
    setCurrentPage(1); // Reset page when market changes
    localStorage.setItem("selectedMarketRaw", newMarket);
  };

  // Memoize filter params to pass to StockScreener
  const formattedMarketCap = useMemo(() => {
    if (marketCapCond !== 'Any' && marketCapValue) {
      return `${marketCapCond === 'Less Than' ? '<' : '>'}${marketCapValue}`;
    }
    return "";
  }, [marketCapCond, marketCapValue]);

  const formattedIndustry = useMemo(() => {
    return industry !== 'Any' ? industry : "";
  }, [industry]);

  const formattedStockPrice = useMemo(() => {
    if (stockPriceCond !== 'Any' && stockPriceValue) {
      return `${stockPriceCond === 'Less Than' ? '<' : '>'}${stockPriceValue}`;
    }
    return "";
  }, [stockPriceCond, stockPriceValue]);

  return (
    <div className="bg-white dark:bg-gray-900 dark:text-white p-4 space-y-4">
      <MainContentHeader />
      <div className="w-40 border border-black dark:border-white text-center mx-auto my-4">
        <MarketSelector
          value={selectedMarketRaw}
          onChange={handleMarketChange}
        />
      </div>
      <DataPanel
        selectedMarket={selectedMarketRaw} // Pass selected market
        // Pass filter states and setters
        marketCapCond={marketCapCond}
        setMarketCapCond={setMarketCapCond}
        marketCapValue={marketCapValue}
        setMarketCapValue={setMarketCapValue}
        stockPriceCond={stockPriceCond}
        setStockPriceCond={setStockPriceCond}
        stockPriceValue={stockPriceValue}
        setStockPriceValue={setStockPriceValue}
        industry={industry}
        setIndustry={setIndustry}
      />
      <StockScreener
        selectedMarket={selectedMarketRaw}
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        // Pass formatted filter params
        filterMarketCap={formattedMarketCap}
        filterIndustry={formattedIndustry}
        filterStockPrice={formattedStockPrice}
      />
    </div>
  );
};

export default Index;