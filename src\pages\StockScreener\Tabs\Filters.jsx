// Filters.jsx (Updated)
import React from "react";
import { Table } from "antd";
import { useSelector } from "react-redux";

export default function Filters({ data }) {
  const selectedFilters = useSelector(state => state.filters.selectedFilters);

  const columns = [
    { title: "Symbol", dataIndex: "symbol", key: "symbol" },
    { title: "Company", dataIndex: "Name", key: "Name" }, // Map "Company" to "Name"
    ...selectedFilters.map(filterName => ({
      title: filterName,
      dataIndex: filterName,
      key: filterName,
      render: (val) => val != null ? val : "--"
    }))
  ];

  // Map backend data format to table dataSource
  const tableData = data.map(item => ({
    symbol: item.symbol,
    ...item.data
  }));

  return (
    <div style={{ overflowX: "auto" }}>
      <Table
        columns={columns}
        dataSource={tableData}
        pagination={false}
        rowKey="symbol"
        scroll={{ x: "max-content" }}
      />
    </div>
  );
}