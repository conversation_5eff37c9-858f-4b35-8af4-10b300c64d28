import { createAsyncThunk } from "@reduxjs/toolkit";
import {
  fetchStocksList,
  fetchStockHeader,
  fetchTimeSeriesData,
  fetchStockQuotesBySymbols,
  getStocksByIndex,
  getAllIndustries,
  getStocksByIndustry,
} from "./stocksService";

/**
 * 1) Fetch a “master” list of stocks from the external API (not used in paginated view).
 */
export const fetchStocks = createAsyncThunk("stocks/fetchStocks", async () => {
  return await fetchStocksList();
});

/**
 * 2) Fetch header details for a single symbol.
 */
export const fetchStockHeaderBySymbol = createAsyncThunk(
  "stocks/fetchStockHeaderBySymbol",
  async (symbol, { rejectWithValue }) => {
    try {
      return await fetchStockHeader(symbol);
    } catch (err) {
      return rejectWithValue(err.message);
    }
  }
);

/**
 * 3) Fetch time-series data for a single symbol.
 */
export const getTimeSeriesData = createAsyncThunk(
  "stocks/getTimeSeriesData",
  async ({ symbol, interval, outputsize }, { rejectWithValue }) => {
    try {
      return await fetchTimeSeriesData(symbol, interval, outputsize);
    } catch (err) {
      return rejectWithValue(err.message);
    }
  }
);

/**
 * 4) Fetch live quotes for an array of symbols.
 */
export const fetchQuotesBySymbols = createAsyncThunk(
  "stocks/fetchQuotesBySymbols",
  async (symbols, { rejectWithValue }) => {
    try {
      return await fetchStockQuotesBySymbols(symbols);
    } catch (err) {
      return rejectWithValue(err.message);
    }
  }
);

/**
 * 8) Thunk to fetch stocks by index (“TASI”, “NUMO”, or “All Market”), with pagination.
 *    Payload: { index, page, pageSize }
 *    Returns: { data: [ … ], total: <number> }
 */
export const fetchMarketStocks = createAsyncThunk(
  "stocks/fetchMarketStocks",
  async ({ index, page, pageSize }, { rejectWithValue }) => {
    try {
      const result = await getStocksByIndex({ index, page, pageSize });
      return result; // { data, totalPages }
    } catch (err) {
      return rejectWithValue(err.message);
    }
  }
);

/**
 * 9) Thunk to fetch the list of all industries.
 */
export const fetchIndustriesList = createAsyncThunk(
  "stocks/fetchIndustriesList",
  async (_, { rejectWithValue }) => {
    try {
      const industries = await getAllIndustries();
      return industries; // string[]
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);

/**
 * 10) Thunk to fetch stocks filtered by a selected industry (no pagination).
 *     Payload: { industry }
 */
export const fetchByIndustry = createAsyncThunk(
  "stocks/fetchByIndustry",
  async ({ industry }, { rejectWithValue }) => {
    try {
      const data = await getStocksByIndustry({ industry });
      return data; // array of { symbol, data: { … } }
    } catch (err) {
      return rejectWithValue(err.response?.data || err.message);
    }
  }
);
