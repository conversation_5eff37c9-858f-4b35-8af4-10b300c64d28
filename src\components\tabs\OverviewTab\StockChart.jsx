import PropTypes from "prop-types";
import { Line } from "react-chartjs-2";

const StockChart = ({ chartData }) => {
  const { data, options, timeRanges, priceOpen, priceClose, change, percentChange } = chartData;

  return (
    <div className="w-full sm:w-3/4 lg:w-2/3 max-w-2xl p-4 bg-white dark:bg-gray-800 rounded-lg shadow-md flex flex-col">
      {/* Time‐Range Buttons (UI only—no handlers wired up here) */}
      <div className="flex flex-wrap gap-2 mb-4 justify-center">
        {timeRanges.map((range) => (
          <button
            key={range}
            className="text-xs sm:text-sm px-2 py-1 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition"
          >
            {range}
          </button>
        ))}
      </div>

      {/* The Line chart */}
      <div className="w-full h-64 mb-4">
        <Line data={data} options={options} />
      </div>

      {/* Price Open / Close / Change Info */}
      <div className="text-center text-xs sm:text-sm text-gray-900 dark:text-white space-y-1">
        <p>
          Price Open: <strong>{priceOpen} USD</strong> | Price Close:{" "}
          <strong>{priceClose} USD</strong>
        </p>
        <p>
          Change:{" "}
          <span className={parseFloat(change) >= 0 ? "text-green-500" : "text-red-500"}>
            {change} USD ({percentChange}%)
          </span>
        </p>
      </div>
    </div>
  );
};

StockChart.propTypes = {
  chartData: PropTypes.shape({
    data: PropTypes.object.isRequired,
    options: PropTypes.object.isRequired,
    timeRanges: PropTypes.arrayOf(PropTypes.string).isRequired,
    priceOpen: PropTypes.string.isRequired,
    priceClose: PropTypes.string.isRequired,
    change: PropTypes.string.isRequired,
    percentChange: PropTypes.string.isRequired,
  }).isRequired,
};

export default StockChart;
