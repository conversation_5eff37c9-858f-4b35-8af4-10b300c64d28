import MainContentHeader from "../../components/common/MainContentHeader";
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchRevenueGrowthData,
  fetchNetProfitGrowthData,
  fetchStockPriceData,
  fetchEstimationRevenue,
  fetchEstimationProfit,
} from "../../redux/valuation/valuationSlice";
import { fetchAllPortfolios } from "../../redux/portfolio/portfolioSlice";
import { fetchStocks } from "../../redux/stocks/stocksActions";
import ValuationTabs from "./ValuationTabs";
import CompanySearch from "./CompanySearch";
import ValuationTable from "./ValuationTable";
import StockInfoCard from "./StockInfoCard";
import ValuationResults from "./ValuationResults";

const Valuation = () => {
  const { t } = useTranslation();
  // const { t, i18n } = useTranslation();
  // const isRTL = i18n.dir() === "rtl";
  const dispatch = useDispatch();

  // Get valuation data from Redux store
  const {
    revenueGrowthData,
    netProfitGrowthData,
    stockPriceData,
    estimationData,
    profitEstimationData,
    loading,
    loadingPriceData,
    loadingEstimation,
    loadingProfitEstimation,
    error,
    priceDataError,
    estimationError,
    profitEstimationError,
  } = useSelector((state) => state.valuation);

  // Get portfolio data from Redux store
  const { portfolios, loading: portfoliosLoading } = useSelector(
    (state) => state.portfolio
  );

  // Get stock data from Redux store
  const { data: allStocks, status: stocksStatus } = useSelector(
    (state) => state.stocks
  );

  // State for selected stock symbol
  const [selectedSymbol, setSelectedSymbol] = useState(""); // Default to Saudi Aramco

  // State for active valuation method
  const [activeTab, setActiveTab] = useState("revenue");

  // Fetch portfolios and stocks data when component mounts
  useEffect(() => {
    // Check if we already have portfolios data
    if (!portfolios || portfolios.length === 0) {
      dispatch(fetchAllPortfolios());
    }

    // Always ensure stocks are loaded for search functionality
    if (stocksStatus === "idle") {
      dispatch(fetchStocks());
    }
  }, [dispatch, portfolios, stocksStatus]);

  // Fetch valuation data when component mounts or symbol changes
  useEffect(() => {
    if (selectedSymbol) {
      dispatch(fetchRevenueGrowthData(selectedSymbol));
      dispatch(fetchNetProfitGrowthData(selectedSymbol));

      // Only fetch stock price data if we have portfolios available
      if (portfolios && portfolios.length > 0) {
        dispatch(fetchStockPriceData(selectedSymbol));
      }
    }
  }, [dispatch, selectedSymbol, portfolios]);

  // Stock data - will be updated when we get API data
  const [stockData, setStockData] = useState({
    name: "",
    ticker: "",
    price: 0,
    currency: "SAR", // Using SAR as per the memory
    change: 0,
    changePercent: 0,
    timestamp: "",
    noStockSelected: true, // Flag to indicate no stock is selected initially
  });

  // Update stock data when symbol changes or when stockPriceData updates
  useEffect(() => {
    if (!selectedSymbol) {
      // No stock selected - show guidance state
      setStockData({
        name: "",
        ticker: "",
        price: 0,
        currency: "SAR",
        change: 0,
        changePercent: 0,
        timestamp: "",
        noStockSelected: true,
      });
      return;
    }

    // Check if we have accurate price data from our new API
    if (stockPriceData && stockPriceData.symbol === selectedSymbol) {
      // Use the more accurate price data from our dedicated API
      setStockData({
        name: stockPriceData.name || "Unknown",
        ticker: stockPriceData.symbol,
        symbol: stockPriceData.symbol,
        price: stockPriceData.price || 0,
        stockPrice: stockPriceData.price || 0,
        currency: stockPriceData.currency || "SAR",
        change: stockPriceData.priceChange || 0,
        priceChange: stockPriceData.priceChange || 0,
        changePercent: stockPriceData.priceChange1D || 0,
        priceChange1D: stockPriceData.priceChange1D || 0,
        timestamp: new Date().toLocaleString("en-US", {
          year: "numeric",
          month: "short",
          day: "numeric",
          hour: "numeric",
          minute: "numeric",
          hour12: true,
          timeZoneName: "short",
        }),
        country: stockPriceData.country || "Saudi Arabia",
        exchange: stockPriceData.exchange || "Tadawul",
        noStockSelected: false, // Stock is selected
      });
    } else {
      // Fallback to the general stocks data if our dedicated API failed
      const selectedStock = allStocks.find(
        (stock) => stock.symbol === selectedSymbol
      );

      if (selectedStock) {
        // Update stockData with data from Redux store
        setStockData({
          name: selectedStock.name || "Unknown",
          ticker: selectedStock.symbol,
          symbol: selectedStock.symbol,
          price: selectedStock.stockPrice || selectedStock.price || 0,
          stockPrice: selectedStock.stockPrice || selectedStock.price || 0,
          currency: selectedStock.currency || "SAR",
          change: selectedStock.priceChange || 0,
          priceChange: selectedStock.priceChange || 0,
          changePercent: selectedStock.priceChange1D || 0,
          priceChange1D: selectedStock.priceChange1D || 0,
          timestamp: new Date().toLocaleString("en-US", {
            year: "numeric",
            month: "short",
            day: "numeric",
            hour: "numeric",
            minute: "numeric",
            hour12: true,
            timeZoneName: "short",
          }),
          country: selectedStock.country || "Saudi Arabia",
          exchange: selectedStock.exchange || "Tadawul",
          noStockSelected: false, // Stock is selected
        });
      } else {
        // No data available - show empty data
        setStockData({
          name: "",
          ticker: selectedSymbol,
          symbol: selectedSymbol,
          price: 0,
          stockPrice: 0,
          currency: "SAR",
          change: 0,
          priceChange: 0,
          changePercent: 0,
          priceChange1D: 0,
          timestamp: new Date().toLocaleString("en-US", {
            year: "numeric",
            month: "short",
            day: "numeric",
            hour: "numeric",
            minute: "numeric",
            hour12: true,
            timeZoneName: "short",
          }),
          dataNotFound: true, // Flag to indicate data was not found
          noStockSelected: false, // Stock is selected but data not found
        });
      }
    }
  }, [selectedSymbol, allStocks, stockPriceData]);

  // Check if data is not found
  // const isRevenueDataNotFound = revenueGrowthData?.dataNotFound || !revenueGrowthData?.response?.data;
  // const isNetProfitDataNotFound = netProfitGrowthData?.dataNotFound || !netProfitGrowthData?.response?.data;

  // Map API data to our component structure
  const historicalData = {
    revenueGrowth: {
      oneYear:
        revenueGrowthData?.historicalPerformance?.revenueGrowth?.oneYear ||
        "0%",
      threeYears:
        revenueGrowthData?.historicalPerformance?.revenueGrowth?.threeYears ||
        "0%",
      fiveYears:
        revenueGrowthData?.historicalPerformance?.revenueGrowth?.fiveYears ||
        "0%",
    },
    profitMargin: {
      oneYear:
        revenueGrowthData?.historicalPerformance?.profitMargin?.oneYear || "0%",
      threeYears:
        revenueGrowthData?.historicalPerformance?.profitMargin?.threeYears ||
        "0%",
      fiveYears:
        revenueGrowthData?.historicalPerformance?.profitMargin?.fiveYears ||
        "0%",
    },
    netProfitGrowth: {
      oneYear: netProfitGrowthData?.netProfitGrowth?.oneYear || "0%",
      threeYears: netProfitGrowthData?.netProfitGrowth?.threeYears || "0%",
      fiveYears: netProfitGrowthData?.netProfitGrowth?.fiveYears || "0%",
    },
    pe: {
      oneYear:
        revenueGrowthData?.historicalPerformance?.peRatio?.oneYear || "0x",
      threeYears:
        revenueGrowthData?.historicalPerformance?.peRatio?.threeYears || "0x",
      fiveYears:
        revenueGrowthData?.historicalPerformance?.peRatio?.fiveYears || "0x",
    },
  };

  // User estimation state
  const [estimations, setEstimations] = useState({
    revenueGrowth: {
      low: "",
      mid: "",
      high: "",
    },
    profitMargin: {
      low: "",
      mid: "",
      high: "",
    },
    netProfitGrowth: {
      low: "",
      mid: "",
      high: "",
    },
    pe: {
      low: "",
      mid: "",
      high: "",
    },
    marginOfSafety: "",
    yearsOfAnalysis: "5", // Default to 5 years for analysis
  });

  // Results state
  const [results, setResults] = useState({
    low: {
      price: "0",
      upside: "0%",
    },
    mid: {
      price: "0",
      upside: "0%",
    },
    high: {
      price: "0",
      upside: "0%",
    },
  });

  // Calculate valuation results when data or estimations change
  useEffect(() => {
    // Only calculate if we have the necessary data
    if (!loading && !error && stockData.price) {
      // Parse current price
      const currentPrice = parseFloat(stockData.price);
      if (isNaN(currentPrice)) return;

      // Parse growth rates (handle both string and number inputs)
      const parseRate = (rate) => {
        // Handle empty strings, null, undefined
        if (!rate && rate !== 0) return 0;

        // Handle empty string specifically
        if (rate === "") return 0;

        // If it's already a number, convert to decimal (divide by 100 for percentage)
        if (typeof rate === "number") {
          return isNaN(rate) ? 0 : rate / 100;
        }

        // If it's a string, remove % sign if present and convert to decimal
        if (typeof rate === "string") {
          const cleanedRate = rate.trim().replace("%", "");
          if (cleanedRate === "") return 0;
          const numericRate = parseFloat(cleanedRate) / 100;
          return isNaN(numericRate) ? 0 : numericRate;
        }

        return 0;
      };

      // Parse years of analysis
      const years = parseInt(estimations.yearsOfAnalysis, 10) || 5;

      // Parse margin of safety
      const safetyMargin = parseRate(estimations.marginOfSafety);

      // Calculate valuations based on active tab
      let lowPrice, midPrice, highPrice;

      if (activeTab === "revenue") {
        // Revenue Growth Method
        const lowGrowth = parseRate(estimations.revenueGrowth.low);
        const midGrowth = parseRate(estimations.revenueGrowth.mid);
        const highGrowth = parseRate(estimations.revenueGrowth.high);

        // Simple future value calculation: P * (1 + g)^n
        lowPrice =
          currentPrice * Math.pow(1 + lowGrowth, years) * (1 - safetyMargin);
        midPrice =
          currentPrice * Math.pow(1 + midGrowth, years) * (1 - safetyMargin);
        highPrice =
          currentPrice * Math.pow(1 + highGrowth, years) * (1 - safetyMargin);
      } else {
        // Net Profit Growth Method (simplified calculation)
        const lowGrowth = parseRate(estimations.netProfitGrowth.low);
        const midGrowth = parseRate(estimations.netProfitGrowth.mid);
        const highGrowth = parseRate(estimations.netProfitGrowth.high);

        lowPrice =
          currentPrice * Math.pow(1 + lowGrowth, years) * (1 - safetyMargin);
        midPrice =
          currentPrice * Math.pow(1 + midGrowth, years) * (1 - safetyMargin);
        highPrice =
          currentPrice * Math.pow(1 + highGrowth, years) * (1 - safetyMargin);
      }

      // Calculate upside percentages
      const lowUpside = ((lowPrice / currentPrice - 1) * 100).toFixed(1);
      const midUpside = ((midPrice / currentPrice - 1) * 100).toFixed(1);
      const highUpside = ((highPrice / currentPrice - 1) * 100).toFixed(1);

      // Update results
      setResults({
        low: {
          price: lowPrice.toFixed(2),
          upside: `${lowUpside}%`,
        },
        mid: {
          price: midPrice.toFixed(2),
          upside: `${midUpside}%`,
        },
        high: {
          price: highPrice.toFixed(2),
          upside: `${highUpside}%`,
        },
      });
    }
  }, [loading, error, stockData.price, estimations, activeTab]);

  // Handle tab change
  const handleTabChange = (tab) => {
    setActiveTab(tab);

    // Fetch appropriate data based on active tab
    if (tab === "revenue" && selectedSymbol) {
      dispatch(fetchRevenueGrowthData(selectedSymbol));
    } else if (tab === "netProfit" && selectedSymbol) {
      dispatch(fetchNetProfitGrowthData(selectedSymbol));
    }
  };

  // Handle analyze action
  const handleAnalyze = () => {
    console.log("Analyzing with current data");

    if (activeTab === "estimation") {
      // For estimation tab, call the revenue estimation API
      handleEstimationAnalyze();
    } else if (activeTab === "netProfit") {
      // For netProfit tab, check if we should call profit estimation API or fetch historical data
      handleNetProfitAnalyze();
    } else {
      // For other tabs, fetch historical data
      dispatch(fetchRevenueGrowthData(selectedSymbol));
      dispatch(fetchNetProfitGrowthData(selectedSymbol));

      // Only fetch stock price data if we have portfolios available
      if (portfolios && portfolios.length > 0) {
        dispatch(fetchStockPriceData(selectedSymbol));
      }
    }
  };

  // Handle estimation API call for revenue method
  const handleEstimationAnalyze = () => {
    if (!selectedSymbol) {
      console.error("No symbol selected for estimation");
      return;
    }

    // Prepare estimation data for API call
    const estimationPayload = {
      symbol: selectedSymbol,
      marginOfSafety: estimations.marginOfSafety,
      yearsOfAnalysis: estimations.yearsOfAnalysis,
      profitMargin: estimations.profitMargin,
      peRatio: estimations.pe,
      revenueGrowth: estimations.revenueGrowth,
    };

    console.log(
      "Calling estimation revenue API with payload:",
      estimationPayload
    );
    dispatch(fetchEstimationRevenue(estimationPayload));
  };

  // Handle net profit tab analyze - can be either API estimation or historical data
  const handleNetProfitAnalyze = () => {
    if (!selectedSymbol) {
      console.error("No symbol selected for net profit analysis");
      return;
    }

    // Check if user has entered estimation parameters for profit margin
    const hasProfitMarginEstimations =
      estimations.profitMargin.low ||
      estimations.profitMargin.mid ||
      estimations.profitMargin.high;

    if (hasProfitMarginEstimations) {
      // User has entered profit margin estimations, call the profit estimation API
      const profitEstimationPayload = {
        symbol: selectedSymbol,
        marginOfSafety: estimations.marginOfSafety,
        yearsOfAnalysis: estimations.yearsOfAnalysis,
        profitMargin: estimations.profitMargin,
        peRatio: estimations.pe,
        netProfitGrowth: estimations.netProfitGrowth,
      };

      console.log(
        "Calling estimation profit API with payload:",
        profitEstimationPayload
      );
      dispatch(fetchEstimationProfit(profitEstimationPayload));
    } else {
      // No profit margin estimations, fetch historical data only
      dispatch(fetchNetProfitGrowthData(selectedSymbol));

      // Only fetch stock price data if we have portfolios available
      if (portfolios && portfolios.length > 0) {
        dispatch(fetchStockPriceData(selectedSymbol));
      }
    }
  };

  // Handle save valuation
  const handleSaveValuation = () => {
    console.log("Saving valuation");
    // Save logic would go here
  };

  // Update estimation value
  const handleEstimationChange = (field, subfield, value) => {
    setEstimations((prev) => ({
      ...prev,
      [field]: {
        ...prev[field],
        [subfield]: value,
      },
    }));
  };

  // Update specific estimation field
  const handleSpecificFieldChange = (field, value) => {
    setEstimations((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <div className="bg-white dark:bg-gray-900 min-h-screen p-4">
      <div className="container mx-auto">
        {/* Header with title */}
        <div className="mb-6">
          <MainContentHeader />
        </div>

        {/* Search bar */}
        <div className="mb-6">
          <CompanySearch
            onSelectStock={(symbol) => {
              setSelectedSymbol(symbol);
            }}
          />
          {(loading ||
            loadingPriceData ||
            portfoliosLoading ||
            stocksStatus === "loading") && (
            <p className="text-gray-500 dark:text-gray-400">
              {stocksStatus === "loading"
                ? "Loading available stocks..."
                : "Loading data..."}
            </p>
          )}
          {(error || priceDataError) && (
            <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
              <p className="text-red-600 dark:text-red-400 font-medium">
                {t("valuation.dataError") || "Data not found"}
              </p>
              <p className="text-red-500 dark:text-red-300 text-sm mt-1">
                {typeof (error || priceDataError) === "string"
                  ? error || priceDataError
                  : t("valuation.noDataForStock") ||
                    "No data available for the selected stock"}
              </p>
            </div>
          )}
        </div>

        {/* User Guidance when no stock is selected */}
        {!selectedSymbol && stocksStatus !== "loading" && (
          <div className="text-center py-8 bg-blue-50 dark:bg-blue-900/20 rounded-lg mb-6">
            <div className="max-w-md mx-auto">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                {t("valuation.welcomeTitle") || "Welcome to Stock Valuation"}
              </h3>
              <p className="text-gray-700 dark:text-gray-200 mb-4">
                {t("valuation.welcomeMessage") ||
                  "Search and select a stock above to begin your valuation analysis"}
              </p>
              <div className="flex items-center justify-center space-x-2 text-sm text-gray-600 dark:text-gray-300">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
                <span>
                  {t("valuation.searchHint") ||
                    "Start by typing a stock name or symbol"}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Tab and Table container - grouped together */}
        <div className="mb-6">
          {/* Tabs */}
          <ValuationTabs activeTab={activeTab} onTabChange={handleTabChange} />

          {/* Content based on active tab */}
          <ValuationTable
            activeTab={activeTab}
            historicalData={historicalData}
            estimations={estimations}
            onEstimationChange={handleEstimationChange}
            onSpecificFieldChange={handleSpecificFieldChange}
            loading={
              loading ||
              loadingPriceData ||
              (activeTab === "estimation" && loadingEstimation) ||
              (activeTab === "netProfit" && loadingProfitEstimation)
            }
          />
        </div>

        {/* Stock info and analyze button */}
        <div className="mb-6 flex items-center gap-4">
          <div className="flex-grow">
            <StockInfoCard
              stockData={stockData}
              loading={loading || loadingPriceData}
            />
          </div>
          <button
            onClick={handleAnalyze}
            className="bg-blue-600 hover:bg-blue-700 text-white py-3 px-12 rounded-lg font-medium text-lg h-full min-w-[180px]"
          >
            {t("valuation.analyze") || "Analyze"}
          </button>
        </div>

        {/* Results */}
        <div className="flex justify-center mb-6">
          <ValuationResults
            results={results}
            currency={stockData.currency}
            onSave={handleSaveValuation}
            apiEstimation={
              activeTab === "estimation"
                ? estimationData
                : activeTab === "netProfit" &&
                  (estimations.profitMargin.low ||
                    estimations.profitMargin.mid ||
                    estimations.profitMargin.high)
                ? profitEstimationData
                : null
            }
            isApiEstimation={
              activeTab === "estimation" ||
              (activeTab === "netProfit" &&
                (estimations.profitMargin.low ||
                  estimations.profitMargin.mid ||
                  estimations.profitMargin.high))
            }
          />
        </div>

        {/* Error handling for estimation API */}
        {activeTab === "estimation" && estimationError && (
          <div className="flex justify-center mb-6">
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 max-w-md">
              <div className="flex items-center">
                <svg
                  className="h-5 w-5 text-red-400 mr-2"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <p className="text-sm text-red-700 dark:text-red-400">
                  {estimationError}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Loading indicator for estimation API */}
        {activeTab === "estimation" && loadingEstimation && (
          <div className="flex justify-center mb-6">
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 max-w-md">
              <div className="flex items-center">
                <svg
                  className="animate-spin h-5 w-5 text-blue-500 mr-2"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  />
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  />
                </svg>
                <p className="text-sm text-blue-700 dark:text-blue-400">
                  {t("valuation.loadingEstimation") ||
                    "Analyzing estimation..."}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Error handling for profit estimation API */}
        {activeTab === "netProfit" && profitEstimationError && (
          <div className="flex justify-center mb-6">
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 max-w-md">
              <div className="flex items-center">
                <svg
                  className="h-5 w-5 text-red-400 mr-2"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <p className="text-sm text-red-700 dark:text-red-400">
                  {profitEstimationError}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Loading indicator for profit estimation API */}
        {activeTab === "netProfit" && loadingProfitEstimation && (
          <div className="flex justify-center mb-6">
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 max-w-md">
              <div className="flex items-center">
                <svg
                  className="animate-spin h-5 w-5 text-blue-500 mr-2"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  />
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  />
                </svg>
                <p className="text-sm text-blue-700 dark:text-blue-400">
                  {t("valuation.loadingProfitEstimation") ||
                    "Analyzing profit estimation..."}
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Valuation;
