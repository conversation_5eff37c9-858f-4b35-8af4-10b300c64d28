import PropTypes from "prop-types";
import { useTranslation } from "react-i18next";
import { Skeleton } from "antd";
import "./ValuationTable.css";

const ValuationTable = ({
  activeTab,
  historicalData,
  estimations,
  onEstimationChange,
  onSpecificFieldChange,
  loading = false,
}) => {
  const { t } = useTranslation();

  // Render skeleton loading rows
  const renderSkeletonRows = () => {
    const rows = [];
    const rowCount = activeTab === "revenue" ? 4 : 3; // Adjust based on number of rows per tab

    for (let i = 0; i < rowCount; i++) {
      rows.push(
        <tr
          key={`skeleton-row-${i}`}
          className="border-b border-gray-200 dark:border-gray-700 w-1/2"
        >
          <td className="py-3 px-4 border-r border-gray-200 dark:border-gray-700">
            <Skeleton.Input active size="small" style={{ width: "80%" }} />
          </td>
          {[...Array(3)].map((_, colIndex) => (
            <td
              key={`skeleton-col-${colIndex}`}
              className="py-3 px-4 text-center"
            >
              <Skeleton.Input
                active
                size="small"
                style={{ width: "50px", maxWidth: "100%" }}
                className="mx-auto"
              />
            </td>
          ))}
        </tr>
      );
    }
    return rows;
  };

  // Helper function to render the table rows based on active tab
  const renderTableRows = () => {
    if (loading) return renderSkeletonRows();
    if (activeTab === "revenue") {
      // Revenue Growth Method - Based on Image 1
      return (
        <>
          <tr className="border-b border-gray-200 dark:border-gray-700">
            <td className="py-3 px-4 font-medium border-r border-gray-200 dark:border-gray-700">
              {t("valuation.revenueGrowth") || "Revenue Growth%"}
            </td>
            <td className="py-3 px-4 text-center">
              {historicalData.revenueGrowth.oneYear}
            </td>
            <td className="py-3 px-4 text-center">
              {historicalData.revenueGrowth.threeYears}
            </td>
            <td className="py-3 px-4 text-center border-r border-gray-200 dark:border-gray-700">
              {historicalData.revenueGrowth.fiveYears}
            </td>
            <td className="py-3 px-4 text-center">
              <span className="flex items-center gap-0.5">
                <input
                  type="number"
                  value={estimations.revenueGrowth.low}
                  onChange={(e) =>
                    onEstimationChange("revenueGrowth", "low", e.target.value)
                  }
                  placeholder="0"
                  className="w-full bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-600 rounded text-center p-1 text-sm"
                />
                <span>%</span>
              </span>
            </td>
            <td className="py-3 px-4 text-center">
              <span className="flex items-center gap-0.5">
                <input
                  type="number"
                  value={estimations.revenueGrowth.mid}
                  onChange={(e) =>
                    onEstimationChange("revenueGrowth", "mid", e.target.value)
                  }
                  placeholder="0"
                  className="w-full bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-600 rounded text-center p-1 text-sm"
                />
                <span>%</span>
              </span>
            </td>
            <td className="py-3 px-4 text-center">
              <span className="flex items-center gap-0.5">
                <input
                  type="number"
                  value={estimations.revenueGrowth.high}
                  onChange={(e) =>
                    onEstimationChange("revenueGrowth", "high", e.target.value)
                  }
                  placeholder="0"
                  className="w-full bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-600 rounded text-center p-1 text-sm"
                />
                <span>%</span>
              </span>
            </td>
          </tr>
          <tr className="border-b border-gray-200 dark:border-gray-700">
            <td className="py-3 px-4 font-medium border-r border-gray-200 dark:border-gray-700">
              {t("valuation.profitMargin") || "Profit Margin"}
            </td>
            <td className="py-3 px-4 text-center">
              {historicalData.profitMargin.oneYear}
            </td>
            <td className="py-3 px-4 text-center">
              {historicalData.profitMargin.threeYears}
            </td>
            <td className="py-3 px-4 text-center border-r border-gray-200 dark:border-gray-700">
              {historicalData.profitMargin.fiveYears}
            </td>
            <td className="py-3 px-4 text-center">
              <span className="flex items-center gap-0.5">
                <input
                  type="number"
                  value={estimations.profitMargin.low}
                  onChange={(e) =>
                    onEstimationChange("profitMargin", "low", e.target.value)
                  }
                  placeholder="0"
                  className="w-full bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-600 rounded text-center p-1 text-sm"
                />
                <span>%</span>
              </span>
            </td>
            <td className="py-3 px-4 text-center">
              <span className="flex items-center gap-0.5">
                <input
                  type="number"
                  value={estimations.profitMargin.mid}
                  onChange={(e) =>
                    onEstimationChange("profitMargin", "mid", e.target.value)
                  }
                  placeholder="0"
                  className="w-full bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-600 rounded text-center p-1 text-sm"
                />
                <span>%</span>
              </span>
            </td>
            <td className="py-3 px-4 text-center">
              <span className="flex items-center gap-0.5">
                <input
                  type="number"
                  value={estimations.profitMargin.high}
                  onChange={(e) =>
                    onEstimationChange("profitMargin", "high", e.target.value)
                  }
                  placeholder="0"
                  className="w-full bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-600 rounded text-center p-1 text-sm"
                />
                <span>%</span>
              </span>
            </td>
          </tr>
          <tr>
            <td className="py-3 px-4 font-medium border-r border-gray-200 dark:border-gray-700">
              {t("valuation.pe") || "P/E"}
            </td>
            <td className="py-3 px-4 text-center">
              {historicalData.pe.oneYear}
            </td>
            <td className="py-3 px-4 text-center">
              {historicalData.pe.threeYears}
            </td>
            <td className="py-3 px-4 text-center border-r border-gray-200 dark:border-gray-700">
              {historicalData.pe.fiveYears}
            </td>
            <td className="py-3 px-4 text-center">
              <span className="flex items-center gap-0.5">
                <input
                  type="number"
                  value={estimations.pe.low}
                  onChange={(e) =>
                    onEstimationChange("pe", "low", e.target.value)
                  }
                  placeholder="0"
                  className="w-full bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-600 rounded text-center p-1 text-sm"
                />
                <span>x</span>
              </span>
            </td>
            <td className="py-3 px-4 text-center">
              <span className="flex items-center gap-0.5">
                <input
                  type="number"
                  value={estimations.pe.mid}
                  onChange={(e) =>
                    onEstimationChange("pe", "mid", e.target.value)
                  }
                  placeholder="0"
                  className="w-full bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-600 rounded text-center p-1 text-sm"
                />
                <span>x</span>
              </span>
            </td>
            <td className="py-3 px-4 text-center">
              <span className="flex items-center gap-0.5">
                <input
                  type="number"
                  value={estimations.pe.high}
                  onChange={(e) =>
                    onEstimationChange("pe", "high", e.target.value)
                  }
                  placeholder="0"
                  className="w-full bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-600 rounded text-center p-1 text-sm"
                />
                <span>x</span>
              </span>
            </td>
          </tr>
        </>
      );
    } else if (activeTab === "netProfit") {
      // Net Profit Growth Method - Based on Image 2
      return (
        <>
          <tr className="border-b border-gray-200 dark:border-gray-700">
            <td className="py-3 px-4 font-medium border-r border-gray-200 dark:border-gray-700">
              {t("valuation.netProfitGrowth") || "Net Profit Growth Growth%"}
            </td>
            <td className="py-3 px-4 text-center">
              {historicalData.netProfitGrowth.oneYear}
            </td>
            <td className="py-3 px-4 text-center">
              {historicalData.netProfitGrowth.threeYears}
            </td>
            <td className="py-3 px-4 text-center border-r border-gray-200 dark:border-gray-700">
              {historicalData.netProfitGrowth.fiveYears}
            </td>
            <td className="py-3 px-4 text-center">
              <span className="flex items-center gap-0.5">
                <input
                  type="number"
                  value={estimations.netProfitGrowth.low}
                  onChange={(e) =>
                    onEstimationChange("netProfitGrowth", "low", e.target.value)
                  }
                  placeholder="0"
                  className="w-full bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-600 rounded text-center p-1 text-sm"
                />
                <span>%</span>
              </span>
            </td>
            <td className="py-3 px-4 text-center">
              <span className="flex items-center gap-0.5">
                <input
                  type="number"
                  value={estimations.netProfitGrowth.mid}
                  onChange={(e) =>
                    onEstimationChange("netProfitGrowth", "mid", e.target.value)
                  }
                  placeholder="0"
                  className="w-full bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-600 rounded text-center p-1 text-sm"
                />
                <span>%</span>
              </span>
            </td>
            <td className="py-3 px-4 text-center">
              <span className="flex items-center gap-0.5">
                <input
                  type="number"
                  value={estimations.netProfitGrowth.high}
                  onChange={(e) =>
                    onEstimationChange(
                      "netProfitGrowth",
                      "high",
                      e.target.value
                    )
                  }
                  placeholder="0"
                  className="w-full bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-600 rounded text-center p-1 text-sm"
                />
                <span>%</span>
              </span>
            </td>
          </tr>
          <tr>
            <td className="py-3 px-4 font-medium border-r border-gray-200 dark:border-gray-700">
              {t("valuation.pe") || "P/E"}
            </td>
            <td className="py-3 px-4 text-center">
              {historicalData.pe.oneYear}
            </td>
            <td className="py-3 px-4 text-center">
              {historicalData.pe.threeYears}
            </td>
            <td className="py-3 px-4 text-center border-r border-gray-200 dark:border-gray-700">
              {historicalData.pe.fiveYears}
            </td>
            <td className="py-3 px-4 text-center">
              <span className="flex items-center gap-0.5">
                <input
                  type="number"
                  value={estimations.pe.low}
                  onChange={(e) =>
                    onEstimationChange("pe", "low", e.target.value)
                  }
                  placeholder="0"
                  className="w-full bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-600 rounded text-center p-1 text-sm"
                />
                <span>x</span>
              </span>
            </td>
            <td className="py-3 px-4 text-center">
              <span className="flex items-center gap-0.5">
                <input
                  type="number"
                  value={estimations.pe.mid}
                  onChange={(e) =>
                    onEstimationChange("pe", "mid", e.target.value)
                  }
                  placeholder="0"
                  className="w-full bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-600 rounded text-center p-1 text-sm"
                />
                <span>x</span>
              </span>
            </td>
            <td className="py-3 px-4 text-center">
              <span className="flex items-center gap-0.5">
                <input
                  type="number"
                  value={estimations.pe.high}
                  onChange={(e) =>
                    onEstimationChange("pe", "high", e.target.value)
                  }
                  placeholder="0"
                  className="w-full bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-600 rounded text-center p-1 text-sm"
                />
                <span>x</span>
              </span>
            </td>
          </tr>
        </>
      );
    } else if (activeTab === "saved") {
      // Saved Valuations
      return (
        <tr>
          <td colSpan="7" className="py-6 px-4 text-center">
            <div className="flex flex-col items-center space-y-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-16 w-16 text-gray-300 dark:text-gray-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1.5}
                  d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                />
              </svg>
              <p className="text-gray-500 dark:text-gray-400 text-lg font-medium">
                {t("valuation.noSavedValuations") || "No saved valuations yet"}
              </p>
              <p className="text-gray-400 dark:text-gray-500 text-sm max-w-md text-center">
                {t("valuation.saveValuationMessage") ||
                  "Your saved valuations will appear here. Use the 'Save The Valuation' button after analyzing a stock."}
              </p>
            </div>
          </td>
        </tr>
      );
    }

    // For other tabs, similar structure would be implemented
    // This is a placeholder for the other valuation methods
    return (
      <tr>
        <td colSpan="7" className="py-4 px-4 text-center text-gray-500">
          {t("valuation.notImplemented") ||
            "This valuation method is not implemented yet"}
        </td>
      </tr>
    );
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-b-lg shadow-sm overflow-hidden border border-gray-200 dark:border-gray-700">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead>
            <tr className="bg-gray-50 dark:bg-gray-700">
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-500 dark:text-gray-300 w-1/5 border-r border-gray-200 dark:border-gray-700">
                {t("valuation.item") || "Item"}
              </th>
              <th
                colSpan="3"
                className="px-4 py-3 text-center text-sm font-medium text-gray-500 dark:text-gray-300 w-2/5 border-r border-gray-200 dark:border-gray-700"
              >
                {t("valuation.historicalPerformance") ||
                  "Historical Performance"}
              </th>
              <th
                colSpan="3"
                className="px-4 py-3 text-center text-sm font-medium text-gray-500 dark:text-gray-300 w-2/5"
              >
                {t("valuation.myEstimation") || "My Estimation"}
              </th>
            </tr>
            <tr className="bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
              <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 w-1/5 border-r border-gray-200 dark:border-gray-700"></th>
              <th className="px-4 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-300">
                {t("valuation.oneYear") || "1 Year"}
              </th>
              <th className="px-4 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-300">
                {t("valuation.threeYears") || "3 Years"}
              </th>
              <th className="px-4 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-300 border-r border-gray-200 dark:border-gray-700">
                {t("valuation.fiveYears") || "5 Years"}
              </th>
              <th className="px-4 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-300">
                {t("valuation.low") || "Low"}
              </th>
              <th className="px-4 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-300">
                {t("valuation.mid") || "Mid"}
              </th>
              <th className="px-4 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-300">
                {t("valuation.high") || "High"}
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {renderTableRows()}
          </tbody>
          <tfoot className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            <tr>
              <td></td>
              <td colSpan="3"></td>
              <td colSpan="3">
                <div className="w-full grid gap-4 bg-gray-50 dark:bg-gray-700 p-3">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium text-gray-500 dark:text-gray-300">
                      {t("valuation.marginOfSafety") || "Margin of safety"}
                    </label>

                    <span className="flex items-center gap-0.5">
                      <input
                        type="number"
                        value={estimations.marginOfSafety}
                        onChange={(e) =>
                          onSpecificFieldChange(
                            "marginOfSafety",
                            e.target.value
                          )
                        }
                        placeholder="0"
                        className="min-w-8 max-w-28 bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-600 rounded text-center p-1 text-sm"
                      />
                      <span>%</span>
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium text-gray-500 dark:text-gray-300">
                      {t("valuation.yearsOfAnalysis") || "Years of Analysis"}
                    </label>

                    <span className="flex items-center gap-0.5">
                      <input
                        type="number"
                        value={estimations.yearsOfAnalysis}
                        onChange={(e) =>
                          onSpecificFieldChange(
                            "yearsOfAnalysis",
                            e.target.value
                          )
                        }
                        className="min-w-8 max-w-28 bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-600 rounded text-center p-1 text-sm"
                      />
                      <span className="invisible">%</span>
                    </span>
                  </div>
                </div>
              </td>
            </tr>
          </tfoot>
        </table>
      </div>

      {/* Additional input fields at the bottom */}
      {/* <div className="flex justify-end p-4 border-t border-gray-200 dark:border-gray-700">
        <div className="w-2/5 grid gap-4 bg-gray-50 dark:bg-gray-700 p-3 rounded">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium text-gray-500 dark:text-gray-300">
              {t("valuation.marginOfSafety") || "Margin of safety"}
            </label>
            <input
              type="number"
              value={estimations.marginOfSafety}
              onChange={(e) =>
                onSpecificFieldChange("marginOfSafety", e.target.value)
              }
              className="w-16 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded text-center p-1 text-sm"
            />
          </div>
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium text-gray-500 dark:text-gray-300">
              {t("valuation.yearsOfAnalysis") || "Years of Analysis"}
            </label>
            <input
              type="number"
              value={estimations.yearsOfAnalysis}
              onChange={(e) =>
                onSpecificFieldChange("yearsOfAnalysis", e.target.value)
              }
              className="w-16 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded text-center p-1 text-sm"
            />
          </div>
        </div>
      </div> */}
    </div>
  );
};

ValuationTable.propTypes = {
  activeTab: PropTypes.string.isRequired,
  historicalData: PropTypes.object.isRequired,
  estimations: PropTypes.object.isRequired,
  onEstimationChange: PropTypes.func.isRequired,
  onSpecificFieldChange: PropTypes.func.isRequired,
  loading: PropTypes.bool,
};

ValuationTable.defaultProps = {
  loading: false,
};

export default ValuationTable;
