import { createBrowserRouter, Navigate } from "react-router-dom";
import Login from "../components/auth/Login.jsx";
import Signup from "../components/auth/Signup.jsx";
import ForgetPassword from "../components/auth/ForgetPassword.jsx";
import ResetPassword from "../components/auth/ResetPassword.jsx";
import VerifyEmail from "../components/auth/VerifyEmail.jsx";

import App from "../App";
import Homepage from "../pages/Homepage";
// import Dashboard from '../pages/Dashboard.jsx';
import Stocks from "../pages/Stocks";
import Watchlist from "../pages/Watchlist";
import Valuation from "../pages/Valuation";
import StockScreener from "../pages/StockScreener";
import PreliminaryFinancialResults from "../pages/PreliminaryFinancialResults/index.jsx";
import InstituteOfResearchAndStudy from "../pages/InstituteOfResearchAndStudy";
import Chart from "../pages/Chart";
import VirtualPortfolio from "../pages/VirtualPortfolio";
import Store from "../pages/Store";
// import StockDetails from "../pages/StockDetails";
import News from "../pages/News";
import ErrorPage from "../pages/ErrorPage.jsx";
import Profile from "../pages/Profile";
import ProtectedRoute from "./ProtectedRoute";
import UserProfile from "../components/auth/UserProfile.jsx";
import AdminPage from "../pages/AdminPage.jsx";
import ProtectedAdminRoute from "./ProtectedAdminRoute.jsx";
import StockDetailsLayout from "../pages/StockDetails/StockDetailsLayout.jsx";
import OverviewTab from "../components/tabs/OverviewTab/OverviewTab.jsx";
import BalanceSheetTab from "../components/tabs/BalanceSheetTab/BalanceSheetTab.jsx";

import IncomeStatementTab from "../components/tabs/IncomeStatementTab/IncomeStatementTab.jsx";
import CashFlowTab from "../components/tabs/CashFlowTab/CashFlowTab.jsx";
import RatioTab from "../components/tabs/RatioTab/RatioTab.jsx";
import ChartTab from "../components/tabs/ChartTab/ChartTab.jsx";
import ProfileTab from "../components/tabs/ProfileTab/ProfileTab.jsx";
import ArticlesDetails from "../pages/InstituteOfResearchAndStudy/ArticlesDetails.jsx";
import PortfolioDetails from "../pages/VirtualPortfolio/PortfolioDetails.jsx";
import StockChart from "../pages/Chart/StockChart.jsx";
import ContractsSummary from "../components/tabs/ContractsSummary/ContractsSummaryTab.jsx";
import ProspectusSummaryTab from "../components/tabs/ProspectusSummary/ProspectusSummaryTab.jsx";

const router = createBrowserRouter([
  {
    path: "/",
    element: <App />,
    errorElement: <ErrorPage />,
    children: [
      { path: "home", element: <Homepage /> },
      { path: "stocks", element: <Stocks /> },
      {
        path: "/stocks/:id",
        element: <StockDetailsLayout />,
        children: [
          { index: true, element: <Navigate to="overview" replace /> },
          { path: "overview", element: <OverviewTab /> },
          { path: "balancesheet", element: <BalanceSheetTab /> },
          { path: "incomestatement", element: <IncomeStatementTab /> },
          { path: "cashflow", element: <CashFlowTab /> },
          { path: "ratio", element: <RatioTab /> },
          { path: "chart", element: <ChartTab /> },
          { path: "profile", element: <ProfileTab /> },
          { path: "contractssummary", element: <ContractsSummary /> },
          { path: "prospectussummary", element: <ProspectusSummaryTab /> },
        ],
      },
      { path: "watchlist", element: <Watchlist /> },
      { path: "valuation", element: <Valuation /> },
      { path: "stock-screener", element: <StockScreener /> },
      { path: "preliminary-financial-results", element: <PreliminaryFinancialResults /> },
      {
        path: "institute-of-research-and-study",
        element: <InstituteOfResearchAndStudy />,

      },
      {
        path: "institute-of-research-and-study/:id",
        element: <ArticlesDetails />,
      },
      { path: "chart", element: <Chart /> },
      {
        path: "chart/:symbol",
        element: <StockChart />,
      },
      
      {
        element: <ProtectedRoute />,
        children: [{ path: "news", element: <News /> }],
      },

      {
        element: <ProtectedAdminRoute requiredRole="admin" />, // Specify requiredRole for admin
        children: [{ path: "admin", element: <AdminPage /> }],
      },

      { path: "virtual-portfolio", element: <VirtualPortfolio /> },
       {
        path: "portfolio/:id",
        element: <PortfolioDetails />,
      },
      { path: "store", element: <Store /> },

     
      { path: "profile", element: <Profile /> },
      { path: "user-profile", element: <UserProfile /> },
    ],
  },
  { path: "/login", element: <Login />, errorElement: <ErrorPage /> },
  { path: "/signup", element: <Signup />, errorElement: <ErrorPage /> },
  {
    path: "/password_reset",
    element: <ForgetPassword />,
    errorElement: <ErrorPage />,
  },
  { path: "/reset", element: <ResetPassword />, errorElement: <ErrorPage /> },
  {
    path: "/auth/verify-email/:token",
    element: <VerifyEmail />,
    errorElement: <ErrorPage />,
  },
]);

export default router;
