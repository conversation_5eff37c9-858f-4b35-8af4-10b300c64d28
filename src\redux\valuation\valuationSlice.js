import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import { fetchStockDetails } from "../portfolio/portfolioSlice";

// Base API URL
const backendURL =
  import.meta.env.VITE_BACKEND_URL || "http://localhost:3000/api";

// Empty data structures for when API data is not available
const mockRevenueGrowthData = {
  response: {
    message: "Data not found for the requested stock.",
    data: null,
  },
  dataNotFound: true,
};

const mockNetProfitGrowthData = {
  response: {
    message: "Data not found for the requested stock.",
    data: null,
  },
  dataNotFound: true,
};

// Async thunk for fetching revenue growth data
export const fetchRevenueGrowthData = createAsyncThunk(
  "valuation/fetchRevenueGrowthData",
  async (symbol, { rejectWithValue }) => {
    try {
      // Check if symbol is provided
      if (!symbol) {
        return rejectWithValue("No stock symbol provided");
      }

      // Use the correct API endpoint
      try {
        const response = await axios.get(
          `${backendURL}/valuation/getRevenueGrowth`,
          {
            params: { symbol },
          }
        );
        console.log(`Successfully fetched revenue growth data for ${symbol}`);
        console.log(response.data);
        return response.data;
      } catch (apiError) {
        // If API returns 404 or any other error, throw to use mock data
        console.warn(
          `API endpoint not available for symbol ${symbol}: ${apiError.message}`
        );
        throw apiError;
      }
    } catch (error) {
      // Log the error but don't fail the request
      console.error(`Falling back to mock revenue growth data for ${symbol}`);

      // Return mock data with a flag indicating it's mock data
      return {
        ...mockRevenueGrowthData,
        isMockData: true,
      };
    }
  }
);

// Async thunk for fetching net profit growth data
export const fetchNetProfitGrowthData = createAsyncThunk(
  "valuation/fetchNetProfitGrowthData",
  async (symbol, { rejectWithValue }) => {
    try {
      // Check if symbol is provided
      if (!symbol) {
        return rejectWithValue("No stock symbol provided");
      }

      // Use the correct API endpoint
      try {
        const response = await axios.get(
          `${backendURL}/valuation/getProfitGrowth`,
          {
            params: { symbol },
          }
        );
        console.log(
          `Successfully fetched net profit growth data for ${symbol}`
        );
        console.log(response.data);
        return response.data;
      } catch (apiError) {
        // If API returns 404 or any other error, throw to use mock data
        console.warn(
          `API endpoint not available for symbol ${symbol}: ${apiError.message}`
        );
        throw apiError;
      }
    } catch (error) {
      // Log the error but don't fail the request
      console.error(
        `Falling back to mock net profit growth data for ${symbol}`
      );

      // Return a properly structured error response
      return rejectWithValue({
        message: `Failed to fetch net profit growth data for ${symbol}`,
        error: error.message
      });
    }
  }
);

// Async thunk to fetch stock price and percentage data from portfolio API
export const fetchStockPriceData = createAsyncThunk(
  "valuation/fetchStockPriceData",
  async (symbol, { dispatch, rejectWithValue, getState }) => {
    try {
      // Check if symbol is provided
      if (!symbol) {
        return rejectWithValue("No stock symbol provided");
      }

      // Get all portfolios from the state
      const { portfolios } = getState().portfolio;
      
      // If there are no portfolios, we can't fetch stock details
      if (!portfolios || portfolios.length === 0) {
        return rejectWithValue("No portfolios available to fetch stock details");
      }
      
      // Use the first portfolio to fetch stock details
      // This is just to get the current price and percentage data for the symbol
      const portfolioId = portfolios[0]?.id;
      
      if (!portfolioId) {
        return rejectWithValue("No portfolio ID available");
      }
      
      // Dispatch the fetchStockDetails thunk from the portfolio slice
      const resultAction = await dispatch(fetchStockDetails(portfolioId));
      
      // Check if the action was fulfilled
      if (fetchStockDetails.fulfilled.match(resultAction)) {
        const stockDetailsData = resultAction.payload;
        
        if (stockDetailsData && stockDetailsData.success && stockDetailsData.data) {
          // Find the stock details for the requested symbol
          const stockDetail = stockDetailsData.data.find(stock => stock.symbol === symbol);
          
          if (stockDetail) {
            return {
              success: true,
              data: {
                symbol: stockDetail.symbol,
                name: stockDetail.name,
                price: stockDetail.price,
                priceChange: stockDetail.day_gain || 0,
                priceChange1D: stockDetail.day_gain_percent || 0,
                currency: "SAR",
                exchange: stockDetail.exchange || "Tadawul",
                country: "Saudi Arabia"
              }
            };
          }
        }
        
        // If we couldn't find the stock in the response, return a generic error
        return rejectWithValue(`Stock details for symbol ${symbol} not found in portfolio data`);
      }
      
      // If the action was rejected, return the error
      return rejectWithValue(resultAction.error?.message || "Failed to fetch stock details from portfolio");
    } catch (error) {
      console.error(`Error fetching stock price data for ${symbol}:`, error);
      return rejectWithValue(error.message || "Failed to fetch stock price data");
    }
  }
);

const initialState = {
  revenueGrowthData: {
    historicalPerformance: {
      revenueGrowth: {
        oneYear: "",
        threeYears: "",
        fiveYears: "",
      },
      profitMargin: {
        oneYear: "",
        threeYears: "",
        fiveYears: "",
      },
      peRatio: {
        oneYear: "",
        threeYears: "",
        fiveYears: "",
      },
    },
  },
  netProfitGrowthData: {
    netProfitGrowth: {
      oneYear: "",
      threeYears: "",
      fiveYears: "",
    },
  },
  stockPriceData: {
    symbol: "",
    name: "",
    price: 0,
    priceChange: 0,
    priceChange1D: 0,
    currency: "SAR",
    exchange: "Tadawul",
    country: "Saudi Arabia",
    timestamp: new Date().toISOString(),
  },
  loading: false,
  loadingPriceData: false,
  error: null,
  priceDataError: null,
};

const valuationSlice = createSlice({
  name: "valuation",
  initialState,
  reducers: {
    resetValuationData: (state) => {
      state.revenueGrowthData = initialState.revenueGrowthData;
      state.netProfitGrowthData = initialState.netProfitGrowthData;
      state.loading = false;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Revenue Growth Method
      .addCase(fetchRevenueGrowthData.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchRevenueGrowthData.fulfilled, (state, action) => {
        state.loading = false;

        // Extract data from the API response
        const responseData =
          action.payload.response?.data?.["Revenue Growth Method"]?.[
            "Historical Performance"
          ];

        if (responseData) {
          state.revenueGrowthData = {
            historicalPerformance: {
              revenueGrowth: {
                oneYear: responseData["Revenue Growth%"]?.["1 Year"] || "",
                threeYears: responseData["Revenue Growth%"]?.["3 Years"] || "",
                fiveYears: responseData["Revenue Growth%"]?.["5 Years"] || "",
              },
              profitMargin: {
                oneYear: responseData["Profit Margin"]?.["1 Year"] || "",
                threeYears: responseData["Profit Margin"]?.["3 Years"] || "",
                fiveYears: responseData["Profit Margin"]?.["5 Years"] || "",
              },
              peRatio: {
                oneYear: responseData["PE Ratio"]?.["1 Year"] || "",
                threeYears: responseData["PE Ratio"]?.["3 Years"] || "",
                fiveYears: responseData["PE Ratio"]?.["5 Years"] || "",
              },
            },
            isMockData: action.payload.isMockData || false,
          };
        }
      })
      .addCase(fetchRevenueGrowthData.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to fetch revenue growth data";
      })

      // Net Profit Growth Method
      .addCase(fetchNetProfitGrowthData.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchNetProfitGrowthData.fulfilled, (state, action) => {
        state.loading = false;

        // Extract data from the API response
        const responseData =
          action.payload.response?.data?.["Net Profit Growth Method"]?.[
            "Historical Performance"
          ];

        if (responseData) {
          state.netProfitGrowthData = {
            netProfitGrowth: {
              oneYear: responseData["Net Profit Growth%"]?.["1 Year"] || "",
              threeYears: responseData["Net Profit Growth%"]?.["3 Years"] || "",
              fiveYears: responseData["Net Profit Growth%"]?.["5 Years"] || "",
            },
            peRatio: {
              oneYear: responseData["PE Ratio"]?.["1 Year"] || "",
              threeYears: responseData["PE Ratio"]?.["3 Years"] || "",
              fiveYears: responseData["PE Ratio"]?.["5 Years"] || "",
            },
            isMockData: action.payload.isMockData || false,
          };
        }
      })
      .addCase(fetchNetProfitGrowthData.rejected, (state, action) => {
        state.loading = false;
        // Handle both string and object error payloads
        if (action.payload && typeof action.payload === 'object' && action.payload.message) {
          state.error = action.payload.message;
        } else {
          state.error = action.payload || "Failed to fetch net profit growth data";
        }
      })

      // Stock Price Data
      .addCase(fetchStockPriceData.pending, (state) => {
        state.loadingPriceData = true;
        state.priceDataError = null;
      })
      .addCase(fetchStockPriceData.fulfilled, (state, action) => {
        state.loadingPriceData = false;
        
        // Handle successful response
        if (action.payload && action.payload.success) {
          const stockData = action.payload.data;
          
          // Update the stock price data in the state
          state.stockPriceData = {
            symbol: stockData.symbol || "",
            name: stockData.name || "",
            price: parseFloat(stockData.price || stockData.stockPrice || 0),
            priceChange: parseFloat(stockData.priceChange || 0),
            priceChange1D: parseFloat(stockData.priceChange1D || stockData.day_gain_percent || 0),
            currency: stockData.currency || "SAR",
            exchange: stockData.exchange || "Tadawul",
            country: stockData.country || "Saudi Arabia",
            timestamp: new Date().toISOString(),
          };
          
          // console.log("Updated stock price data in valuation slice:", state.stockPriceData);
        }
      })
      .addCase(fetchStockPriceData.rejected, (state, action) => {
        state.loadingPriceData = false;
        state.priceDataError = action.payload || "Failed to fetch stock price data";
      });
  },
});

export const { resetValuationData } = valuationSlice.actions;

export default valuationSlice.reducer;
