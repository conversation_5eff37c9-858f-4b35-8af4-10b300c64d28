import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import {
  fetchStockScreenerOverview,
  fetchPerformance,
  fetchEarnings,
  fetchFilterTab,
} from './screenerService';

export const loadScreenerOverview = createAsyncThunk(
  'screener/loadOverview',
  async (params) => {
    const response = await fetchStockScreenerOverview(params);
    return response;
  }
);

export const loadPerformance = createAsyncThunk(
  'screener/loadPerformance',
  async (params) => {
    const response = await fetchPerformance(params);
    return response;
  }
);

export const loadEarnings = createAsyncThunk(
  'screener/loadEarnings',
  async (params) => {
    const response = await fetchEarnings(params);
    return response;
  }
);

export const loadFilterTab = createAsyncThunk(
  'screener/loadFilterTab',
  async (params) => {
    const response = await fetchFilterTab(params);
    return response;
  }
);

const initialState = {
  overview: { data: [], totalPages: 0 },
  performance: { data: [], totalPages: 0 },
  earnings: { data: [], totalPages: 0 },
  status: 'idle',
  error: null,
  filters: { data: [], totalPages: 0 }, // Added for filters tab
};

const screenerSlice = createSlice({
  name: 'screener',
  initialState,
  extraReducers: builder => {
    builder
      // Overview
      .addCase(loadScreenerOverview.pending, state => { state.status = 'loading'; })
      .addCase(loadScreenerOverview.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.overview.data = action.payload.data;
        state.overview.totalPages = action.payload.totalPages;
      })
      .addCase(loadScreenerOverview.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message;
      })
      // Performance
      .addCase(loadPerformance.pending, state => { state.status = 'loading'; })
      .addCase(loadPerformance.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.performance.data = action.payload.data;
        state.performance.totalPages = action.payload.totalPages;
      })
      .addCase(loadPerformance.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message;
      })
      // Earnings
      .addCase(loadEarnings.pending, state => { state.status = 'loading'; })
      .addCase(loadEarnings.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.earnings.data = action.payload.data;
        state.earnings.totalPages = action.payload.totalPages;
      })
      .addCase(loadEarnings.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message;
      })

      // Filters
      .addCase(loadFilterTab.pending, state => { state.status = 'loading'; })
      .addCase(loadFilterTab.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.filters.data = action.payload.data;
        state.filters.totalPages = action.payload.totalPages;
      })
      .addCase(loadFilterTab.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message;
      });
  }
});

export default screenerSlice.reducer;