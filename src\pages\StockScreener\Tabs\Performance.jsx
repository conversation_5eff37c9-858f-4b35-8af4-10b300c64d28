import React from "react";
import { Table, Checkbox } from "antd";

export default function Performance({ data, selectedRowKeys = [], onChange = () => {} }) {
  const columns = [
    {
      title: "",
      dataIndex: "key",
      render: (key) => (
        <Checkbox
          checked={selectedRowKeys.includes(key)}
          onChange={() =>
            onChange(
              selectedRowKeys.includes(key)
                ? selectedRowKeys.filter((k) => k !== key)
                : [...selectedRowKeys, key]
            )
          }
        />
      ),
    },
    { title: "Symbol", dataIndex: "symbol" },
    { title: "Company", dataIndex: "name" },
    { title: "Price", dataIndex: "sharePrice" },
    {
      title: "1D Change",
      dataIndex: "dailyPriceChange",
      render: (txt) => (
        <span className={typeof txt === "string" && txt.startsWith("-") ? "text-red-500" : "text-green-500"}>
          {txt ?? "--"}
        </span>
      ),
    },
    {
      title: "1W Change",
      dataIndex: "weeklyPriceChange",
      render: (txt) => (
        <span className={typeof txt === "string" && txt.startsWith("-") ? "text-red-500" : "text-green-500"}>
          {txt ? `${txt}%` : "--"}
        </span>
      ),
    },
    {
      title: "1M Change",
      dataIndex: "monthlyPriceChange",
      render: (txt) => (
        <span className={typeof txt === "string" && txt.startsWith("-") ? "text-red-500" : "text-green-500"}>
          {txt ? `${txt}%` : "--"}
        </span>
      ),
    },
    {
      title: "YTD Change",
      dataIndex: "yearToDatePriceChange",
      render: (txt) => (
        <span className={typeof txt === "string" && txt.startsWith("-") ? "text-red-500" : "text-green-500"}>
          {txt ? `${txt}%` : "--"}
        </span>
      ),
    },
    {
      title: "1Y Change",
      dataIndex: "annualPriceChange",
      render: (txt) => (
        <span className={typeof txt === "string" && txt.startsWith("-") ? "text-red-500" : "text-green-500"}>
          {txt ? `${txt}%` : "--"}
        </span>
      ),
    },
  ];

  const tableData = data.map((item, index) => ({
    key: index,
    symbol: item.symbol,
    ...item.data
  }));

  return <Table columns={columns} dataSource={tableData} pagination={false} rowKey="key" />;
}