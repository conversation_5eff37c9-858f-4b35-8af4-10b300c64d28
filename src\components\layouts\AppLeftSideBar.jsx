import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faArrowLeft,
  faArrowRight,
  faHome,
  faChartLine,
  faBookmark,
  faFileContract,
  faChartColumn,
  faFileCircleQuestion,
  faBuildingColumns,
  faChartSimple,
  faWallet,
  faStore,
  faUserShield, // new icon for admin
} from "@fortawesome/free-solid-svg-icons";
import PropTypes from "prop-types";
import SidebarItem from "./SidebarItem.jsx"; // Import SidebarItem
import { useTranslation } from "react-i18next";
import { useState, useEffect, useCallback } from "react";
import { useSelector } from "react-redux";

const AppLeftSideBar = ({ isCollapsed, toggleSidebar }) => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.dir() === "rtl";
  const [items, setItems] = useState([]);

  // Retrieve user info from Redux
  const user = useSelector((state) => state.user.userInfo);
  const isAdmin = user && user.role?.includes("admin");

  // Create sidebar items with hard-coded translations as a fallback
  const getSidebarItems = useCallback(() => {
    // Check if translation is available
    const homeLabel = i18n.exists("sidebar.home")
      ? t("sidebar.home")
      : isRTL
      ? "الرئيسية"
      : "Home";

    const stocksLabel = i18n.exists("sidebar.stocks")
      ? t("sidebar.stocks")
      : isRTL
      ? "الأسهم"
      : "Stocks";

    const watchlistLabel = i18n.exists("sidebar.watchlist")
      ? t("sidebar.watchlist")
      : isRTL
      ? "قائمة المراقبة"
      : "Watchlist";

    const valuationLabel = i18n.exists("sidebar.valuation")
      ? t("sidebar.valuation")
      : isRTL
      ? "التقييم"
      : "Valuation";

    const stockScreenerLabel = i18n.exists("sidebar.stockScreener")
      ? t("sidebar.stockScreener")
      : isRTL
      ? "فاحص الأسهم"
      : "Stock Screener";

    const preliminaryFinancialResultsLabel = i18n.exists("sidebar.preliminaryFinancialResults")
      ? t("sidebar.preliminaryFinancialResults")
      : isRTL
      ? "آخر الشركات المبلغ عنها"
      : "Preliminary Financial Results";

    const instituteOfResearchLabel = i18n.exists("sidebar.instituteOfResearch")
      ? t("sidebar.instituteOfResearch")
      : isRTL
      ? "معهد البحث والدراسة"
      : "Institute of Research & Study";

    const chartLabel = i18n.exists("sidebar.chart")
      ? t("sidebar.chart")
      : isRTL
      ? "الرسم البياني"
      : "Chart";

    const newsLabel = i18n.exists("sidebar.news")
      ? t("sidebar.news")
      : isRTL
      ? "الأخبار"
      : "News";

    const virtualPortfolioLabel = i18n.exists("sidebar.virtualPortfolio")
      ? t("sidebar.virtualPortfolio")
      : isRTL
      ? "المحفظة الافتراضية"
      : "Virtual Portfolio";

    const storeLabel = i18n.exists("sidebar.store")
      ? t("sidebar.store")
      : isRTL
      ? "المتجر"
      : "Store";

    const stockDetailsLabel = i18n.exists("sidebar.stockDetails")
      ? t("sidebar.stockDetails")
      : isRTL
      ? "صفحة تفاصيل السهم"
      : "Stock Details Page";

    return [
      { href: "/home", label: homeLabel, icon: faHome },
      { href: "/stocks", label: stocksLabel, icon: faChartLine },
      { href: "/watchlist", label: watchlistLabel, icon: faBookmark },
      { href: "/valuation", label: valuationLabel, icon: faFileContract },
      {
        href: "/stock-screener",
        label: stockScreenerLabel,
        icon: faChartColumn,
      },
      {
        href: "/preliminary-financial-results",
        label: preliminaryFinancialResultsLabel,
        icon: faFileCircleQuestion,
      },
      {
        href: "/institute-of-research-and-study",
        label: instituteOfResearchLabel,
        icon: faBuildingColumns,
      },
      { href: "/chart", label: chartLabel, icon: faChartSimple },
      { href: "/news", label: newsLabel, icon: faChartSimple },
      {
        href: "/virtual-portfolio",
        label: virtualPortfolioLabel,
        icon: faWallet,
      },
      { href: "/store", label: storeLabel, icon: faStore },
      // { href: "/stock-details-page", label: stockDetailsLabel, icon: faStore },
    ];
  }, [t, i18n, isRTL]);

  useEffect(() => {
    // Update the sidebar items when language changes
    setItems(getSidebarItems());
  }, [t, i18n.language, getSidebarItems]); // Include getSidebarItems in dependency array

  // Determine which arrow icon to use based on sidebar state and language direction
  const getArrowIcon = () => {
    if (isRTL) {
      // RTL language (Arabic)
      return isCollapsed ? faArrowLeft : faArrowRight;
    } else {
      // LTR language (English)
      return isCollapsed ? faArrowRight : faArrowLeft;
    }
  };

  // Button position class based on language direction
  const buttonPositionClass = isRTL
    ? "absolute -left-2.5 top-1/2 transform -translate-y-1/2" // Left side for RTL
    : "absolute -right-2.5 top-1/2 transform -translate-y-1/2"; // Right side for LTR (default)

  return (
    <aside
      className={`${
        isCollapsed ? "w-14" : "w-60"
      } h-full bg-black/5 dark:bg-gray-800 transition-all duration-300 ease-in-out relative`}
    >
      <button
        onClick={toggleSidebar}
        className={`${buttonPositionClass} w-6 h-6 flex justify-center items-center hover:bg-gray-300 dark:hover:bg-gray-600 rounded-full transition cursor-pointer focus:outline-none bg-gray-200 dark:bg-gray-700`}
      >
        <FontAwesomeIcon icon={getArrowIcon()} />
      </button>

      {/* Sidebar Navigation */}
      <nav className="mt-4">
        <ul>
          {items.map((item) => (
            <SidebarItem
              key={item.href}
              href={item.href}
              label={item.label}
              isCollapsed={isCollapsed}
              icon={item.icon}
            />
          ))}

          {/* Conditionally render the admin button below "News" */}
          {isAdmin && (
            <SidebarItem
              href="/admin"
              label="Admin"
              isCollapsed={isCollapsed}
              icon={faUserShield}
            />
          )}
        </ul>
      </nav>
    </aside>
  );
};

AppLeftSideBar.propTypes = {
  isCollapsed: PropTypes.bool.isRequired,
  toggleSidebar: PropTypes.func.isRequired,
};

export default AppLeftSideBar;