import React, { useState, useEffect } from "react";
import { Modal, Checkbox, Input } from "antd";
import { SearchOutlined } from "@ant-design/icons";

const FilterModal = ({
  visible,
  onClose,
  title = "Indicators",
  selectedFilters,
  onChange,
  filterCategories,
}) => {
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    if (visible) {
      setSearchTerm("");
    }
  }, [visible]);

  const handleOk = () => {
    onClose();
  };

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value.toLowerCase());
  };

  const handleCheckboxChange = (item, checked) => {
    if (checked) {
      onChange([...selectedFilters, item]);
    } else {
      onChange(selectedFilters.filter((f) => f !== item));
    }
  };

  return (
    <Modal
      title={<b>{title}</b>}
      open={visible}
      onOk={handleOk}
      onCancel={onClose}
      width={1000}
      destroyOnClose
      maskClosable
      style={{ top: 50 }}
      styles={{
        body: {
          maxHeight: "70vh",
          overflowY: "auto",
          paddingRight: "12px",
        },
      }}
    >
      <Input
        placeholder="Search"
        prefix={<SearchOutlined />}
        className="mb-4"
        onChange={handleSearchChange}
        allowClear
      />

      <div className="space-y-6">
        {filterCategories.map((category) => {
          const filteredOptions =
            searchTerm.trim() === ""
              ? category.filters
              : category.filters.filter((filter) =>
                  filter.toLowerCase().includes(searchTerm)
                );

          return (
            <div key={category.title} className="mb-2">
              <h3 className="font-semibold text-sm mb-2 text-gray-700 border-b pb-1">
                {category.title}
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
                {filteredOptions.map((item) => (
                  <Checkbox
                    key={item}
                    value={item}
                    checked={selectedFilters.includes(item)}
                    onChange={(e) =>
                      handleCheckboxChange(item, e.target.checked)
                    }
                  >
                    {item}
                  </Checkbox>
                ))}
              </div>
            </div>
          );
        })}
      </div>
    </Modal>
  );
};

export default FilterModal;