import React, { useState, useEffect, useRef } from 'react';
import { useDispatch } from 'react-redux';
import FilterModal from './FilterModal' // adjust path if necessary
import {
  loadScreenerOverview,
  loadPerformance,
  loadEarnings,
} from '../../redux/screener/screenerSlice';
import { Button, Space, Select, AutoComplete } from 'antd';
import { PlusOutlined, DownOutlined } from '@ant-design/icons';
import axios from 'axios';

const { Option } = Select;

const marketCapOptions = ['1B', '5B', '10B', '50B', '100B'].map((v) => ({
  label: v,
  value: v,
}));
const priceOptions = ['1', '50', '100'].map((v) => ({ label: v, value: v }));

export default function DataPanel({ selectedMarket = 'all', currentPage, activeTab }) {
  const dispatch = useDispatch();

  // Quick-add states
  const [modalVisible, setModalVisible] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [options, setOptions] = useState([]);

  // Inline filter states
  const [marketCapCond, setMarketCapCond] = useState('Any');
  const [marketCapValue, setMarketCapValue] = useState(null);
  const [stockPriceCond, setStockPriceCond] = useState('Any');
  const [stockPriceValue, setStockPriceValue] = useState(null);
  const [industry, setIndustry] = useState('Any');
  const [industries, setIndustries] = useState([]);

  // NEW: categories fetched from backend
  const [categories, setCategories] = useState({});

  // Dropdown state and ref
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);

  // Fetch industries once
  useEffect(() => {
    axios
      .get(`${import.meta.env.VITE_BACKEND_URL}/industries/get-all-industries`)
      .then((r) => setIndustries(r.data.response.data.industries || []))
      .catch(console.error);
  }, []);

  // Dispatch only the activeTab’s API whenever any filter, selectedMarket, currentPage, or activeTab changes
  useEffect(() => {
    const marketIndex =
      selectedMarket === 'all' ? 'All Market' : selectedMarket.toUpperCase();
    const params = {
      page: Number(currentPage),
      index: marketIndex,
      "Market Capitalization":
        marketCapCond !== 'Any' && marketCapValue
          ? `${marketCapCond === 'Less Than' ? '<' : '>'}${marketCapValue}`
          : "",
      "Industry": industry !== 'Any' ? industry : "",
      "Share Price":
        stockPriceCond !== 'Any' && stockPriceValue
          ? `${stockPriceCond === 'Less Than' ? '<' : '>'}${stockPriceValue}`
          : "",
    };

    // Dispatch only the API for the active tab
    if (activeTab === 'overview') {
      dispatch(loadScreenerOverview(params));
    } else if (activeTab === 'performance') {
      dispatch(loadPerformance(params));
    } else if (activeTab === 'earnings') {
      dispatch(loadEarnings(params));
    }
  }, [
    marketCapCond,
    marketCapValue,
    stockPriceCond,
    stockPriceValue,
    industry,
    selectedMarket,
    currentPage,
    activeTab,
    dispatch,
  ]);

  const handleIndustryChange = (val) => {
    setIndustry(val);
  };

  // Quick-add stubs (not implemented)
  const handleSearch = (v) => {
    setSearchTerm(v);
  };
  const handleSelect = (v) => {
    /* Implement if needed */
  };

  // Handle clicks outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    };
    if (isDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isDropdownOpen]);

  // NEW: Fetch categories from backend endpoint
  const fetchCategories = async () => {
    try {
      const resp = await axios.get(
        `${import.meta.env.VITE_BACKEND_URL}/filter/get-indicators`
      );
      // Expecting { groupIndicators: { "Category A": [...], ... } }
      setCategories(resp.data.groupIndicators || {});
    } catch (err) {
      console.error("Failed to fetch filter indicators:", err);
      setCategories({});
    }
  };

  // When user clicks “Add Filter”, load categories then open modal
  const onAddFilterClick = async () => {
    await fetchCategories();
    setModalVisible(true);
  };

  return (
    <div className="p-4 flex flex-col gap-4">
      <Button onClick={() => setIsDropdownOpen(!isDropdownOpen)}>
        Filters <DownOutlined />
      </Button>
      {isDropdownOpen && (
        <div
          ref={dropdownRef}
          style={{
            width: '100%',
            padding: 12,
            background: '#fff',
            borderRadius: 4,
            border: '1px solid #d9d9d9',
            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
          }}
        >
          <Space style={{ width: '100%', marginBottom: 12 }} align="start">
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={onAddFilterClick} // UPDATED
            >
              Add Filter
            </Button>
            <AutoComplete
              options={options}
              onSelect={handleSelect}
              onSearch={handleSearch}
              value={searchTerm}
              placeholder="Search filters…"
              allowClear
              style={{ flex: 1 }}
            />
          </Space>
        </div>
      )}

      <div className="flex flex-wrap gap-8">
        <Space align="center">
          <span className="font-medium">Market Cap:</span>
          <Select
            value={marketCapCond}
            onChange={(v) => {
              setMarketCapCond(v);
              setMarketCapValue(null);
            }}
            style={{ width: 140 }}
          >
            <Option value="Any">Any</Option>
            <Option value="Less Than">Less Than</Option>
            <Option value="More Than">More Than</Option>
          </Select>
          {marketCapCond !== 'Any' && (
            <Select
              value={marketCapValue}
              onChange={setMarketCapValue}
              placeholder="Value"
              style={{ width: 120 }}
            >
              {marketCapOptions.map((o) => (
                <Option key={o.value} value={o.value}>
                  {o.label}
                </Option>
              ))}
            </Select>
          )}
        </Space>

        <Space align="center">
          <span className="font-medium">Industry:</span>
          <Select
            value={industry}
            onChange={handleIndustryChange}
            style={{ width: 200 }}
          >
            <Option value="Any">Any</Option>
            {industries.map((i) => (
              <Option key={i} value={i}>
                {i}
              </Option>
            ))}
          </Select>
        </Space>

        <Space align="center">
          <span className="font-medium">Stock Price:</span>
          <Select
            value={stockPriceCond}
            onChange={(v) => {
              setStockPriceCond(v);
              setStockPriceValue(null);
            }}
            style={{ width: 140 }}
          >
            <Option value="Any">Any</Option>
            <Option value="Less Than">Less Than</Option>
            <Option value="More Than">More Than</Option>
          </Select>
          {stockPriceCond !== 'Any' && (
            <Select
              value={stockPriceValue}
              onChange={setStockPriceValue}
              placeholder="Value"
              style={{ width: 100 }}
            >
              {priceOptions.map((o) => (
                <Option key={o.value} value={o.value}>
                  {o.label}
                </Option>
              ))}
            </Select>
          )}
        </Space>
      </div>

      {/* NEW: Pass categories down into FilterModal */}
      <FilterModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        categories={categories}
      />
    </div>
  );
}
