import React from "react";
import { Table, Checkbox } from "antd";

export default function Overview({ data, selectedRowKeys = [], onChange = () => {} }) {
  const columns = [
    {
      title: "",
      dataIndex: "key",
      render: (key) => (
        <Checkbox
          checked={selectedRowKeys.includes(key)}
          onChange={() =>
            onChange(
              selectedRowKeys.includes(key)
                ? selectedRowKeys.filter((k) => k !== key)
                : [...selectedRowKeys, key]
            )
          }
        />
      ),
    },
    { title: "Symbol", dataIndex: "symbol" },
    { title: "Company", dataIndex: "name" },
    { title: "Price", dataIndex: "sharePrice" },
    {
      title: "1D Change",
      dataIndex: "dailyPriceChange",
      render: (txt) => (
        <span className={typeof txt === "string" && txt.startsWith("-") ? "text-red-500" : "text-green-500"}>
          {txt ?? "--"}
        </span>
      ),
    },
    {
      title: "Div. Yield",
      dataIndex: "dividendYieldPercent",
      render: (txt) => (
        <span className={typeof txt === "string" && txt.startsWith("-") ? "text-red-500" : "text-green-500"}>
          {txt ? `${txt}%` : "--"}
        </span>
      ),
    },
    { title: "P/E Ratio", dataIndex: "priceToEarningsRatio" },
    { title: "52W High", dataIndex: "_52High" },
    { title: "52W Low", dataIndex: "_52Low" },
    { title: "Market Cap", dataIndex: "marketCapitalization" },
  ];

  const tableData = data.map((item, index) => ({
    key: index,
    symbol: item.symbol,
    ...item.data
  }));

  return <Table columns={columns} dataSource={tableData} pagination={false} rowKey="key" />;
}