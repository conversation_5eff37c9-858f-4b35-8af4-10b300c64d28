// src/pages/stocks/Index.jsx

import React, { useEffect, useState, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Table, Dropdown, <PERSON>u, Button } from "antd";
import { DownOutlined, EditOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";

import MainContentHeader from "../../components/common/MainContentHeader";
import MarketSelector from "../../components/common/MarketSelector";
import StocksFilterModal from "./StocksFilterModal";
const backendURL = import.meta.env.VITE_BACKEND_URL;

import {
  fetchMarketStocks,
  fetchIndustriesList,
  fetchByIndustry,
} from "../../redux/stocks/stocksActions";

/**
 * Convert the raw selector value ("tasi"/"numo"/"all")
 * into exactly what the backend expects ("TASI"/"NUMO"/"All Market").
 */
function normalizeMarket(raw) {
  switch (raw.toLowerCase()) {
    case "tasi":
      return "TASI";
    case "numo":
      return "NUMO";
    case "all":
      return "All Market";
    default:
      return raw;
  }
}

const Index = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  // 1) Grab Redux state
  const { data, totalPages, status, industries, error } = useSelector(
    (state) => state.stocks
  );

  // 2) Local UI state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10); // ← default to 10 per page

  const [selectedMarketRaw, setSelectedMarketRaw] = useState(
    () => localStorage.getItem("selectedMarketRaw") || "all"
  );

  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);
  const [dynamicFilters, setDynamicFilters] = useState([]);
  const [selectedIndustry, setSelectedIndustry] = useState(null);
  const [indicatorCategories, setIndicatorCategories] = useState({});

  // 3) Fetch industries on mount
  useEffect(() => {
    dispatch(fetchIndustriesList());
  }, [dispatch]);

  // 4) Whenever selectedMarketRaw or pageSize changes → reset to page 1
  //    The actual data fetch will be handled by effect (5) due to state changes.
  useEffect(() => {
    localStorage.setItem("selectedMarketRaw", selectedMarketRaw);
    setSelectedIndustry(null);
    setCurrentPage(1); // This state change will trigger effect (5)
                       // if currentPage wasn't 1, or if selectedMarketRaw/pageSize
                       // (which are deps of effect 5) changed.
  }, [selectedMarketRaw, pageSize]); // dispatch is removed as no action is dispatched here.
                                     // setCurrentPage and setSelectedIndustry setters are stable.

  // 5) Whenever currentPage, selectedMarketRaw, or pageSize changes → fetch that page
  useEffect(() => {
    const apiIndex = normalizeMarket(selectedMarketRaw);
    dispatch(
      fetchMarketStocks({
        index: apiIndex,
        page: currentPage,
        pageSize,
      })
    );
  }, [currentPage, pageSize, selectedMarketRaw, dispatch]);

  // 6) When an industry is chosen, fetchByIndustry (no pagination)
  const onIndustrySelect = ({ key }) => {
    setSelectedIndustry(key);
    setCurrentPage(1); // Reset to page 1 for consistency, though fetchByIndustry might not use it
    dispatch(
      fetchByIndustry({
        industry: key,
      })
    );
  };

  // Build the Ant Design Menu for industries
  const IndustriesMenu = (
    <Menu
      onClick={onIndustrySelect}
      style={{
        maxHeight: "300px",
        width: "250px",
        overflowY: "auto",
      }}
    >
      {industries.map((ind) => (
        <Menu.Item key={ind}>{ind}</Menu.Item>
      ))}
    </Menu>
  );

  // 7) Base columns
  const baseColumns = [
    { title: "Symbol", dataIndex: "symbol", key: "symbol" },
    {
      title: "Company Name",
      dataIndex: ["data", "Name"],
      key: "name",
      render: (text) => text || "—",
    },
    {
      title: "Stock Price",
      dataIndex: ["data", "Share Price"],
      key: "price",
      render: (value) => (value != null ? value : "—"),
    },
    {
      title: "1D Change (%)",
      dataIndex: ["data", "Daily Price Change"],
      key: "change",
      render: (value) => (value != null ? value : "—"),
    },
    {
      title: "Div Yield (%)",
      dataIndex: ["data", "Dividend Yield Percent"],
      key: "div_yield",
      render: (value) => (value != null ? Number(value).toFixed(2) : "—"),
    },
    {
      title: "P/E Ratio",
      dataIndex: ["data", "Price to Earnings Ratio"],
      key: "pe_ratio",
      render: (value) => (value != null ? value : "—"),
    },
    {
      title: "52-wk Low",
      dataIndex: ["data", "52-Week-Low"],
      key: "low",
      render: (value) => (value != null ? value : "—"),
    },
    {
      title: "52-wk High",
      dataIndex: ["data", "52-Week-High"],
      key: "high",
      render: (value) => (value != null ? value : "—"),
    },
    {
      title: "Market Cap",
      dataIndex: ["data", "Market Capitalization"],
      key: "market_cap",
      render: (value) => (value != null ? Number(value).toLocaleString() : "—"),
    },
  ];

  // 8) Dynamic columns chosen via the filter modal
  const dynamicColumns = dynamicFilters.map((filter) => ({
    title: filter,
    dataIndex: ["data", filter],
    key: filter,
    render: (value) => (value != null ? value : "N/A"),
  }));

  const combinedColumns = [...baseColumns, ...dynamicColumns];

  // 9) Callback when StocksFilterModal “Apply” button is pressed
  const handleFiltersUpdate = useCallback((filters) => {
    setDynamicFilters(filters);
  }, []);

  // 10) Fetch categories from backend, then open modal
  const fetchIndicatorCategories = async () => {
    try {
      const response = await fetch(
        `${backendURL}/filter/get-indicators`
      );
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const json = await response.json();
      setIndicatorCategories(json.groupIndicators || {});
    } catch (err) {
      console.error("Failed to fetch indicators:", err);
      setIndicatorCategories({});
    }
  };

  const onEditClick = async () => {
    await fetchIndicatorCategories();
    setIsFilterModalOpen(true);
  };

  // Compute “totalItems” for Ant Design based on `totalPages` × `pageSize`.
  const totalItems =
    typeof totalPages === "number" ? totalPages * pageSize : 0;

  return (
    <div className="stocks-table bg-white dark:bg-gray-900 dark:text-white p-4">
      <MainContentHeader />

      {/* Market selector (buttons for “tasi” / “numo” / “all”) */}
      <div className="w-40 border border-black dark:border-white text-center mx-auto my-4">
        <MarketSelector
          value={selectedMarketRaw}
          onChange={setSelectedMarketRaw}
        />
      </div>

      {/* Filter modal for extra indicators */}
      <StocksFilterModal
        visible={isFilterModalOpen}
        onClose={() => setIsFilterModalOpen(false)}
        title="Indicators"
        onFiltersApply={handleFiltersUpdate}
        selectedFilters={dynamicFilters}
        categories={indicatorCategories}
      />

      {/* Display any errors */}
      {error && (
        <div className="text-red-600 text-center mb-4">
          {typeof error === "string" ? error : "Failed to load data."}
        </div>
      )}

      <Table
        columns={combinedColumns}
        dataSource={data}
        rowKey="symbol"
        loading={status === "loading"}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          total: totalItems, // ← use totalPages × pageSize
          showSizeChanger: true,
          pageSizeOptions: ["10", "20", "30", "40"],
          onChange: (page, size) => {
            // Check if size has changed, if so, pageSize effect will handle page reset
            if (pageSize !== size) {
                setPageSize(size);
                // setCurrentPage(1) will be handled by the useEffect for pageSize change
            } else {
                setCurrentPage(page);
            }
          },
        }}
        scroll={{ x: "max-content" }}
        onRow={(record) => ({
          onClick: () => navigate(`/stocks/${record.symbol}`),
        })}
        bordered
        title={() => (
          <div
            className="flex justify-between items-center px-2"
            style={{ borderBottom: "1px solid #f0f0f0" }}
          >
            {/* Industry selector on left */}
            <Dropdown
              overlay={IndustriesMenu}
              placement="bottomLeft"
              getPopupContainer={(trigger) => trigger.parentElement}
            >
              <Button>
                {selectedIndustry || "Select Industry"} <DownOutlined />
              </Button>
            </Dropdown>

            {/* “Edit” filters button on right */}
            <Button
              icon={<EditOutlined />}
              className="border px-3 py-1"
              onClick={onEditClick}
            >
              Edit
            </Button>
          </div>
        )}
      />
    </div>
  );
};

export default Index;