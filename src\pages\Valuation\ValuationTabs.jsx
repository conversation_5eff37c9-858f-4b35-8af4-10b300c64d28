import { useTranslation } from "react-i18next";
import PropTypes from "prop-types";

const ValuationTabs = ({ activeTab, onTabChange }) => {
  const { t } = useTranslation();

  const tabs = [
    // { id: "pe", label: t("valuation.tabs.peModel") || "P/E Model" },
    {
      id: "revenue",
      label: t("valuation.tabs.revenueGrowth") || "Revenue Growth Method",
    },
    {
      id: "netProfit",
      label: t("valuation.tabs.netProfitGrowth") || "Net Profit Growth Method",
    },
    // {
    //   id: "saved",
    //   label: t("valuation.tabs.savedValuation") || "Saved Valuation",
    // },
  ];

  return (
    // <div className="grid grid-cols-4 border border-b-0 border-gray-200 dark:border-gray-700 rounded-t-lg overflow-hidden">
    <div className="grid grid-cols-4 overflow-hidden">
      <button
        className={`px-6 py-3 font-medium text-sm focus:outline-none transition-colors
            ${"bg-gray-100 dark:bg-gray-700 text-blue-600 dark:text-blue-400 border border-b-0 border-gray-200 dark:border-gray-700 rounded-t-lg"}`}
      >
        {t("valuation.tabs.peModel") || "P/E Model"}
      </button>

      {tabs.map((tab) => (
        <button
          key={tab.id}
          className={`px-6 py-3 font-medium text-sm focus:outline-none transition-colors cursor-pointer rounded-t-lg
            ${
              activeTab === tab.id
                ? "bg-blue-50 dark:bg-blue-950 text-gray-900 dark:text-white border-b-2 border-blue-600 dark:border-blue-500"
                : "bg-white dark:bg-gray-900 text-gray-600 dark:text-gray-400 hover:bg-blue-100 dark:hover:bg-blue-900 border-b border-gray-200 dark:border-gray-600"
            }`}
          onClick={() => onTabChange(tab.id)}
        >
          {tab.label}
        </button>
      ))}

      <button
        className={`px-6 py-3 font-medium text-sm focus:outline-none transition-colors
            ${"bg-gray-100 dark:bg-gray-700 text-green-600 dark:text-green-400 border border-b-0 border-gray-200 dark:border-gray-700 rounded-t-lg "}`}
      >
        {t("valuation.tabs.savedValuation") || "Saved Valuation"}
      </button>
    </div>
  );
};

ValuationTabs.propTypes = {
  activeTab: PropTypes.string.isRequired,
  onTabChange: PropTypes.func.isRequired,
};

export default ValuationTabs;
