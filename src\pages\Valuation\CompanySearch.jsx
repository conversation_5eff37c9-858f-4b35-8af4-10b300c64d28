import { useState, useEffect, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useSelector, useDispatch } from "react-redux";
import { fetchStocks } from "../../redux/stocks/stocksActions";
import { Input, Dropdown } from "antd";
import { SearchOutlined } from "@ant-design/icons";
import debounce from "lodash.debounce";

const CompanySearch = ({ onSelectStock }) => {
  const { t } = useTranslation();
  const [query, setQuery] = useState("");
  const [filteredStocks, setFilteredStocks] = useState([]);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dispatch = useDispatch();
  const {
    data: allStocks,
    status,
    loading,
  } = useSelector((state) => state.stocks);

  // Fetch stocks on mount
  useEffect(() => {
    if (status === "idle") {
      dispatch(fetchStocks());
    }
  }, [dispatch, status]);

  // Debounced search handler
  const debouncedSearch = useMemo(
    () =>
      debounce((value) => {
        if (!value) {
          setFilteredStocks([]);
          setDropdownOpen(false);
          return;
        }
        const q = value.toLowerCase();

        // If allStocks is not available yet, show no data message
        if (!allStocks || !Array.isArray(allStocks) || allStocks.length === 0) {
          // Set a special message to indicate no data is available
          setFilteredStocks([]);
          setDropdownOpen(true); // Show dropdown with message
          return;
        }

        const results = allStocks
          .filter(
            (s) =>
              s && // Ensure stock object exists
              ((s.symbol && s.symbol.toLowerCase().includes(q)) ||
                (s.name && s.name.toLowerCase().includes(q)))
          )
          .slice(0, 10);

        setFilteredStocks(results);
        setDropdownOpen(results.length > 0);
      }, 300),
    [allStocks]
  );

  // Handle stock selection
  const handleSelectStock = (stock) => {
    if (onSelectStock && stock && stock.symbol) {
      onSelectStock(stock.symbol);
    }
    setQuery(""); // Clear input after selection
    setFilteredStocks([]); // Clear suggestions
    setDropdownOpen(false);
  };

  // Clean up debounce on unmount
  useEffect(() => () => debouncedSearch.cancel(), [debouncedSearch]);

  const handleInputChange = (e) => {
    const value = e.target.value;
    setQuery(value);
    debouncedSearch(value);
  };

  const dropdownContent = (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
      {loading && (
        <div className="flex justify-center items-center py-4">
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-indigo-500"></div>
          <span className="ml-2 text-gray-600 dark:text-gray-300">
            {t("valuation.searching") || "Searching..."}
          </span>
        </div>
      )}

      {!loading && filteredStocks.length === 0 && query && (
        <div className="p-4 text-gray-500 dark:text-gray-400 text-center">
          {t("valuation.noResults") || "No results found"}
        </div>
      )}

      {!loading &&
        filteredStocks.length === 0 &&
        (!allStocks || !Array.isArray(allStocks) || !allStocks.length) && (
          <div className="p-4 text-red-500 dark:text-red-400 text-center font-medium">
            {t("valuation.noStockData") || "No stock data available"}
          </div>
        )}

      {filteredStocks.map((stock) => (
        <div
          key={stock.symbol || stock.name || Math.random()}
          onClick={() => handleSelectStock(stock)}
          className="p-3 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer flex justify-between border-b border-gray-100 dark:border-gray-700"
        >
          <div>
            <div className="flex items-center">
              <div className="bg-gray-200 dark:bg-gray-600 rounded p-1 mr-2 w-12 text-center text-xs text-gray-800 dark:text-gray-200">
                {stock.symbol || "N/A"}
              </div>
              <span className="text-gray-900 dark:text-white">
                {stock.name || "Unknown"}
              </span>
            </div>
          </div>
          <div className="text-right">
            {stock.stockPrice && (
              <div className="text-gray-900 dark:text-white">
                {stock.stockPrice} SAR
              </div>
            )}
            {stock.priceChange1D !== undefined && (
              <div
                className={`text-xs ${
                  parseFloat(stock.priceChange1D || 0) >= 0
                    ? "text-red-500"
                    : "text-green-500"
                }`}
              >
                {parseFloat(stock.priceChange1D || 0) >= 0 ? "▲" : "▼"}{" "}
                {Math.abs(parseFloat(stock.priceChange1D || 0)).toFixed(2)}%
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <div className="relative w-full max-w-xl mx-auto mb-8">
      <div className="relative">
        <Input
          placeholder={
            t("valuation.searchPlaceholder") ||
            "Search for stocks, by name or symbol"
          }
          className="w-full py-2 px-4 pr-10 bg-white dark:!bg-gray-800 border border-gray-300 dark:!border-gray-600 rounded-lg shadow-sm text-gray-900 dark:!text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          value={query}
          onChange={handleInputChange}
          onClick={() => setDropdownOpen(filteredStocks.length > 0)}
          size="large"
          suffix={
            <SearchOutlined className="text-gray-500 dark:text-gray-400" />
          }
          style={{ height: "48px" }}
        />

        {/* Custom styling */}
        <style>{`
          .ant-input::placeholder {
            color: #9ca3af !important;
          }

          .dark .ant-input::placeholder {
            color: #9ca3af !important;
          }
        `}</style>

        {/* Search results dropdown */}
        {dropdownOpen && (
          <div className="absolute top-full left-0 right-0 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg z-50 mt-1">
            <div className="border-b border-gray-200 dark:border-gray-700"></div>

            <div className="max-h-80 overflow-y-auto">
              {loading && (
                <div className="flex justify-center items-center py-4">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-indigo-500"></div>
                  <span className="ml-2 text-gray-600 dark:text-gray-300">
                    {t("valuation.searching") || "Searching..."}
                  </span>
                </div>
              )}

              {!loading && filteredStocks.length === 0 && query && (
                <div className="p-4 text-gray-500 dark:text-gray-400 text-center">
                  {t("valuation.noResults") || "No results found"}
                </div>
              )}

              {filteredStocks.length > 0
                ? filteredStocks.map((stock) => (
                    <div
                      key={stock.symbol || stock.name || Math.random()}
                      className="border-b border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                      onClick={() => handleSelectStock(stock)}
                    >
                      <div className="flex justify-between items-center px-4 py-3">
                        <div>
                          <div className="font-medium text-gray-800 dark:text-white">
                            {stock.name || "Unknown"}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">
                            {stock.symbol || "N/A"} :{" "}
                            {stock.exchange || "Tadawul"} (
                            {stock.country || "Saudi Arabia"})
                          </div>
                        </div>
                        <div>
                          <div className="text-right font-medium text-gray-900 dark:text-white">
                            {stock.stockPrice ? `${stock.stockPrice} SAR` : ""}
                          </div>
                          {stock.priceChange1D !== undefined && (
                            <div
                              className={`text-right font-medium ${
                                parseFloat(stock.priceChange1D || 0) >= 0
                                  ? "text-red-600 dark:text-red-400"
                                  : "text-green-600 dark:text-green-400"
                              }`}
                            >
                              {parseFloat(stock.priceChange1D || 0) >= 0
                                ? "▲"
                                : "▼"}{" "}
                              {Math.abs(
                                parseFloat(stock.priceChange1D || 0)
                              ).toFixed(2)}
                              %
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                : null}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CompanySearch;
