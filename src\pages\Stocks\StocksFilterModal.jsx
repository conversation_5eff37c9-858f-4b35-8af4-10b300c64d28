import React, { useState, useEffect } from "react";
import { Modal, Checkbox, Input } from "antd";
import { SearchOutlined } from "@ant-design/icons";

const StocksFilterModal = ({
  visible,
  onClose,
  title = "Indicators",
  onFiltersApply,
  selectedFilters = [],
  categories = {}, // <-- Now expecting an object of categories from props
}) => {
  const [localFilters, setLocalFilters] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");

  // Whenever the modal is opened, initialize localFilters from props
  useEffect(() => {
    if (visible) {
      setLocalFilters(selectedFilters);
    }
  }, [visible, selectedFilters]);

  const handleOk = () => {
    onFiltersApply(localFilters);
    onClose();
  };

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value.toLowerCase());
  };

  // Convert the categories (object) into an array of { title, filters } for iteration
  // categories prop looks like: { "Group A": ["Ind1", "Ind2"], "Group B": ["Ind3", ...], ... }
  const categoryArray = Object.entries(categories).map(
    ([title, filters]) => ({ title, filters })
  );

  return (
    <Modal
      title={<b>{title}</b>}
      open={visible}
      onOk={handleOk}
      onCancel={onClose}
      width={1000}
      destroyOnClose
      maskClosable
      style={{ top: 50 }}
      bodyStyle={{
        maxHeight: "70vh",
        overflowY: "auto",
        paddingRight: "12px",
      }}
    >
      <Input
        placeholder="Search"
        prefix={<SearchOutlined />}
        className="mb-4"
        onChange={handleSearchChange}
        allowClear
      />

      <div className="space-y-6">
        {categoryArray.map((category) => {
          // Filter each category’s filters by the search term
          const filteredOptions =
            searchTerm.trim() === ""
              ? category.filters
              : category.filters.filter((filter) =>
                  filter.toLowerCase().includes(searchTerm)
                );

          return (
            <div key={category.title} className="mb-2">
              <h3 className="font-semibold text-sm mb-2 text-gray-700 border-b pb-1">
                {category.title}
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
                {filteredOptions.map((item) => (
                  <Checkbox
                    key={item}
                    value={item}
                    checked={localFilters.includes(item)}
                    onChange={(e) => {
                      const { checked } = e.target;
                      if (checked) {
                        setLocalFilters((prev) => [...prev, item]);
                      } else {
                        setLocalFilters((prev) =>
                          prev.filter((f) => f !== item)
                        );
                      }
                    }}
                  >
                    {item}
                  </Checkbox>
                ))}
                {filteredOptions.length === 0 && (
                  <div className="text-gray-500 italic">No matches</div>
                )}
              </div>
            </div>
          );
        })}
        {categoryArray.length === 0 && (
          <div className="text-center text-gray-600">Loading indicators...</div>
        )}
      </div>
    </Modal>
  );
};

export default StocksFilterModal;
