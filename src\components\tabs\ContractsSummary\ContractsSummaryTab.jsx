import React from "react";

const contracts = [
  {
    title:
      "Arabian Internet and Communications Services Company (solutions) Signs Contract with Saudi Telecom Company (stc)",
    rows: [
      ["Date of Announcement of the Award", "2025-01-26 (Corresponding to 1446-10-13)"],
      ["Entity with which the Contract was Signed", "Saudi Telecom Company"],
      ["Date of Signing the Contract", "2025-01-24 (Corresponding to 1446-10-13)"],
      ["Contract Value", "SAR 492,935,312"],
      ["Contract Subject Matter", "5G network"],
      ["Financial Impact and the Relevant Period", "Will appear starting from the 3rd quarter of the year 2025."],
      ["Related Parties", "Largest shareholder with 79% ownership"]
    ]
  },
  {
    title:
      "Arabian Internet and Communications Services Company (solutions) Signs Revenue-Sharing Contract with Sports Boulevard Foundation",
    rows: [
      ["Date of Announcement of the Award", "2024-12-15 (Corresponding to 1446-06-13)"],
      ["Entity with which the Contract was Signed", "Sports Boulevard Foundation"],
      ["Date of Signing the Contract", "2024-12-15 (Corresponding to 1446-06-13)"],
      ["Contract Value", "Contract covers newly added 5G solutions’ revenues as per the latest audited annual financial statements for the year 2024."],
      ["Contract Subject Matter", "5G solutions revenue share"],
      ["Contract Duration", "5 years"],
      ["Financial Impact and the Relevant Period", "Financial impact to appear from 1st quarter of year 2025."],
      ["Related Parties", "N/A"]
    ]
  },
  {
    title:
      "Arabian Internet and Communications Services Company (solutions) Announces Renewal of Contract with Saudi Telecom Company (stc)",
    rows: [
      ["Date of Announcement of the Award", "2024-12-10 (Corresponding to 1446-06-09)"],
      ["Entity with which the Contract was Signed", "Saudi Telecom Company"],
      ["Date of Signing the Contract", "2024-12-10 (Corresponding to 1446-06-09)"],
      ["Contract Value", "SAR 153,197,867"],
      ["Contract Subject Matter", "IT operations"],
      ["Financial Impact and the Relevant Period", "Will appear starting from the 1st quarter of the year 2025."],
      ["Related Parties", "stc is considered the largest shareholder with 79% of solutions shares"]
    ]
  },
  {
    title:
      "Arabian Internet and Communications Services Company (solutions) Signs Framework Agreement with Saudi Aramco",
    rows: [
      ["Date of Announcement of the Award", "2024-10-30 (Corresponding to 1445-08-29)"],
      ["Contract Subject Matter", "Operating, distributing, and maintaining the computing devices and its accessories for Saudi Aramco"],
      ["Entity with which the Contract was Signed", "Saudi Aramco"],
      ["Date of Signing the Contract", "2024-09-30 (Corresponding to 1445-08-26)"],
      ["Contract Value", "Covers new 5G solutions’ revenues as per the latest audited annual financial statements for the year 2024."],
      ["Contract Duration", "5 years"],
      ["Financial Impact and the Relevant Period", "Financial impact will appear starting from the 1st quarter of the year 2024."],
      ["Related Parties", "N/A"]
    ]
  },
  {
    title:
      "Arabian Internet and Communications Services Company (solutions) Signs Framework Agreement with Digital Centers for Data and Telecommunications Company (Center3)",
    rows: [
      ["Date of Announcement of the Award", "2024-09-16 (Corresponding to 1445-02-11)"],
      ["Entity with which the Contract was Signed", "Digital Centers for Data and Telecommunications Company (Center3)"],
      ["Date of Signing the Contract", "2024-09-16 (Corresponding to 1445-02-11)"],
      ["Contract Subject Matter", "Provision of computing devices and accessories"],
      ["Contract Value", "No direct financial impact on the company’s financial statement."],
      ["Financial Impact and the Relevant Period", "N/A"],
      ["Related Parties", "Center3 is wholly owned by stc"]
    ]
  },
  {
    title:
      "Arabian Internet and Communications Services Company (solutions) Signs Framework Agreement with Digital Centers for Data and Telecommunications Company (Center3)",
    rows: [
      ["Date of Announcement of the Award", "2024-08-29 (Corresponding to 1445-01-25)"],
      ["Entity with which the Contract was Signed", "Digital Centers for Data and Telecommunications Company (Center3)"],
      ["Date of Signing the Contract", "2024-08-29 (Corresponding to 1445-01-25)"],
      ["Contract Subject Matter", "Providing computing devices and accessories"],
      ["Contract Value", "No direct financial impact on the company’s financial statement."],
      ["Financial Impact and the Relevant Period", "N/A"],
      ["Related Parties", "Center3 is wholly owned by stc"]
    ]
  }
];

const ContractsSummary = () => {
  return (
    <div className="space-y-6 p-4">
      {contracts.map((contract, index) => (
        <div
          key={index}
          className="border border-gray-300 rounded-lg shadow p-4 bg-white"
        >
          <h2 className="text-lg font-semibold mb-3 text-gray-800">
            {contract.title}
          </h2>
          <table className="w-full text-sm text-left">
            <thead>
              <tr className="bg-gray-100 text-gray-700">
                <th className="py-2 px-4 font-medium">Items</th>
                <th className="py-2 px-4 font-medium">Details</th>
              </tr>
            </thead>
            <tbody>
              {contract.rows.map(([items, Details], i) => (
                <tr
                  key={i}
                  className="border-t border-gray-200 last:border-b"
                >
                  <td className="py-2 px-4 text-gray-700 font-medium w-1/3">
                    {items}
                  </td>
                  <td className="py-2 px-4 text-gray-900">{Details}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ))}
    </div>
  );
};

export default ContractsSummary;
