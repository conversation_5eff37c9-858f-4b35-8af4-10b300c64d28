// import React from "react";
// import {
//   <PERSON><PERSON><PERSON>,
//   Bar,
//   <PERSON>Axis,
//   Y<PERSON><PERSON>s,
//   CartesianGrid,
//   Tooltip,
//   ResponsiveContainer,
//   Cell,
// } from "recharts";

// const TotalAssetsChart = ({ data, headers }) => {
//   if (!data || !data.values) return null;

//   const chartData = headers.map((header) => ({
//     period: header,
//     value: parseFloat(data.values[header]?.replace(/,/g, "") || 0),
//   }));

//   return (
//     <div className="mt-6 p-4 bg-white dark:bg-gray-700 rounded-lg shadow">
//       <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
//         {data.item_name_ar} (Selected Asset)
//       </h3>
//       <ResponsiveContainer width="100%" height={300}>
//         <BarChart data={chartData}>
//           <CartesianGrid strokeDasharray="3 3" />
//           <XAxis dataKey="period" />
//           <YAxis tickFormatter={(val) => `${val / 1_000_000}M`} />
//           <Tooltip formatter={(val) => `${val.toLocaleString()}`} />
//           <Bar dataKey="value">
//             {chartData.map((entry, index) => (
//               <Cell
//                 key={`cell-${index}`}
//                 fill={entry.value < 0 ? "#ef4444" : "#38bdf8"} // red for negative, blue for positive
//               />
//             ))}
//           </Bar>
//         </BarChart>
//       </ResponsiveContainer>
//     </div>
//   );
// };

// export default TotalAssetsChart;









import React from "react";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Cell,
} from "recharts";

const TotalAssetsChart = ({ data, headers }) => {
  if (!data || !data.values) return null;

  const chartData = headers.map((header) => {
    const rawValue = parseFloat(data.values[header]?.replace(/,/g, "") || 0);
    return {
      period: header,
      originalValue: rawValue,               // Keep original value for tooltip
      displayValue: Math.abs(rawValue),      // Always draw upward
    };
  });

  return (
    <div className="mt-6 p-4 bg-white dark:bg-gray-700 rounded-lg shadow">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 text-center">
        {data.item_name_ar} (Selected Asset)
      </h3>
      <ResponsiveContainer width="100%" height={400}>
        <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="period" />
          <YAxis
            tickFormatter={(val) => `${val / 1_000_000}M`}
            label={{
              value: 'Value',
              angle: -90,
              position: 'insideLeft',
              style: { fill: '#8884d8' },
            }}
          />
          <Tooltip
            formatter={(value, name, props) =>
              props.payload.originalValue.toLocaleString()
            }
            labelFormatter={(label) => `Period: ${label}`}
          />
          <Bar dataKey="displayValue" radius={[4, 4, 0, 0]}>
            {chartData.map((entry, index) => (
              <Cell
                key={`cell-${index}`}
                fill={entry.originalValue < 0 ? "#ef4444" : "#38bdf8"} // red for negative
              />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default TotalAssetsChart;

