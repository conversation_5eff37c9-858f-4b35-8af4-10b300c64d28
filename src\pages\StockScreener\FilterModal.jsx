import React, { useState, useEffect } from 'react';
import { Modal, Checkbox, Input } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { useDispatch, useSelector } from 'react-redux';
import { addFilter, clearFilters } from '../../redux/filter/filterSlice';

const FilterModal = ({
  visible,
  onClose,
  title = "Select screener filters (79 total)", // 🔁 Default title
  categories = {}, // NEW: categories object from parent
}) => {
  const dispatch = useDispatch();
  const selectedFilters = useSelector((state) => state.filters.selectedFilters);
  const [localSelected, setLocalSelected] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (visible) {
      setLocalSelected(selectedFilters);
    }
  }, [visible, selectedFilters]);

  const handleOk = () => {
    dispatch(clearFilters());
    localSelected.forEach((f) => dispatch(addFilter(f)));
    onClose();
  };

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value.toLowerCase());
  };

  // Transform the incoming categories object into an array of { title, filters }
  // categories shape: { "Overview": ["Name", "52-Week-Low", …], "Company Info": [ … ], … }
  const categoryArray = Object.entries(categories).map(([title, filters]) => ({
    title,
    filters,
  }));

  return (
    <Modal
      title={<b>{title}</b>}
      open={visible}
      onOk={handleOk}
      onCancel={onClose}
      width={1000}
      destroyOnClose
      maskClosable={true}
      style={{ top: 50 }}
      bodyStyle={{
        maxHeight: "70vh",
        overflowY: "auto",
        paddingRight: "12px",
      }}
    >
      <Input
        placeholder="Search"
        prefix={<SearchOutlined />}
        className="mb-4"
        onChange={handleSearchChange}
        allowClear
      />

      <div className="space-y-6">
        {categoryArray.map((category) => {
          // Filter each category’s options by the search term
          const filteredOptions =
            searchTerm.trim() === ''
              ? category.filters
              : category.filters.filter((filter) =>
                  filter.toLowerCase().includes(searchTerm)
                );

          return (
            <div key={category.title} className="mb-2">
              <h3 className="font-semibold text-sm mb-2 text-gray-700 border-b pb-1">
                {category.title}
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
                {filteredOptions.map((item) => (
                  <Checkbox
                    key={item}
                    value={item}
                    checked={localSelected.includes(item)}
                    onChange={(e) => {
                      const { checked } = e.target;
                      if (checked) {
                        setLocalSelected((prev) => [...prev, item]);
                      } else {
                        setLocalSelected((prev) =>
                          prev.filter((f) => f !== item)
                        );
                      }
                    }}
                  >
                    {item}
                  </Checkbox>
                ))}
                {filteredOptions.length === 0 && (
                  <div className="text-gray-500 italic">No matches</div>
                )}
              </div>
            </div>
          );
        })}

        {categoryArray.length === 0 && (
          <div className="text-center text-gray-600">
            Loading indicators...
          </div>
        )}
      </div>
    </Modal>
  );
};

export default FilterModal;
