import React from "react";
import { Table, Checkbox } from "antd";

export default function Earnings({ data, selectedRowKeys = [], onChange = () => {} }) {
  const columns = [
    {
      title: "",
      dataIndex: "key",
      render: (key) => (
        <Checkbox
          checked={selectedRowKeys.includes(key)}
          onChange={() =>
            onChange(
              selectedRowKeys.includes(key)
                ? selectedRowKeys.filter((k) => k !== key)
                : [...selectedRowKeys, key]
            )
          }
        />
      ),
    },
    { title: "Symbol", dataIndex: "symbol" },
    { title: "Company", dataIndex: "name" },
    {
      title: "EPS TTM",
      dataIndex: "eps",
      render: (txt) => String(txt ?? "N/A"),
    },
    {
      title: "Rev. Growth TTM",
      dataIndex: "revenueGrowthRate",
      render: (txt) => (
        <span className={typeof txt === "string" && txt.startsWith("-") ? "text-red-500" : "text-green-500"}>
          {txt ? `${txt}%` : "--"}
        </span>
      ),
    },
    {
      title: "Profit Growth TTM",
      dataIndex: "netProfitGrowthRate",
      render: (txt) => (
        <span className={typeof txt === "string" && txt.startsWith("-") ? "text-red-500" : "text-green-500"}>
          {txt ? `${txt}%` : "--"}
        </span>
      ),
    },
  ];

  const tableData = data.map((item, index) => ({
    key: index,
    symbol: item.symbol,
    ...item.data
  }));

  return <Table columns={columns} dataSource={tableData} pagination={false} rowKey="key" />;
}