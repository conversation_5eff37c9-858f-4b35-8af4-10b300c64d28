import PropTypes from "prop-types";
import { useTranslation } from "react-i18next";

const ValuationResults = ({
  results,
  currency,
  onSave,
  apiEstimation,
  isApiEstimation = false,
}) => {
  const { t } = useTranslation();

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden w-80">
      <div className="p-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
        <h3 className="text-base text-center font-semibold text-gray-800 dark:text-gray-200">
          {t("valuation.estimationPrice") || "My Estimation Price"}
        </h3>
      </div>

      <div className="p-0">
        <table className="w-full text-sm">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th className="px-4 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-300">
                {t("valuation.low") || "Low"}
              </th>
              <th className="px-4 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-300">
                {t("valuation.mid") || "Mid"}
              </th>
              <th className="px-4 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-300">
                {t("valuation.high") || "High"}
              </th>
            </tr>
          </thead>
          <tbody>
            {isApiEstimation && apiEstimation ? (
              <>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <td className="px-4 py-3 text-center text-gray-900 dark:text-white">
                    {apiEstimation.estimationPrices.low.toFixed(2)} {currency}
                  </td>
                  <td className="px-4 py-3 text-center text-gray-900 dark:text-white">
                    {apiEstimation.estimationPrices.mid.toFixed(2)} {currency}
                  </td>
                  <td className="px-4 py-3 text-center text-gray-900 dark:text-white">
                    {apiEstimation.estimationPrices.high.toFixed(2)} {currency}
                  </td>
                </tr>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <td
                    className={`px-4 py-3 text-center font-medium ${
                      apiEstimation.percentageChanges.low >= 0
                        ? "text-green-600 dark:text-green-400"
                        : "text-red-600 dark:text-red-400"
                    }`}
                  >
                    {apiEstimation.percentageChanges.low.toFixed(1)}%
                  </td>
                  <td
                    className={`px-4 py-3 text-center font-medium ${
                      apiEstimation.percentageChanges.mid >= 0
                        ? "text-green-600 dark:text-green-400"
                        : "text-red-600 dark:text-red-400"
                    }`}
                  >
                    {apiEstimation.percentageChanges.mid.toFixed(1)}%
                  </td>
                  <td
                    className={`px-4 py-3 text-center font-medium ${
                      apiEstimation.percentageChanges.high >= 0
                        ? "text-green-600 dark:text-green-400"
                        : "text-red-600 dark:text-red-400"
                    }`}
                  >
                    {apiEstimation.percentageChanges.high.toFixed(1)}%
                  </td>
                </tr>
              </>
            ) : (
              <>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <td className="px-4 py-3 text-center text-gray-900 dark:text-white">
                    {results.low?.price} {currency}
                  </td>
                  <td className="px-4 py-3 text-center text-gray-900 dark:text-white">
                    {results.mid?.price} {currency}
                  </td>
                  <td className="px-4 py-3 text-center text-gray-900 dark:text-white">
                    {results.high?.price} {currency}
                  </td>
                </tr>
                <tr>
                  <td
                    className={`px-4 py-3 text-center font-medium ${
                      results.low?.upside &&
                      parseFloat(results.low.upside.replace("%", "")) >= 0
                        ? "text-green-600 dark:text-green-400"
                        : "text-red-600 dark:text-red-400"
                    }`}
                  >
                    {results.low?.upside}
                  </td>
                  <td
                    className={`px-4 py-3 text-center font-medium ${
                      results.mid?.upside &&
                      parseFloat(results.mid.upside.replace("%", "")) >= 0
                        ? "text-green-600 dark:text-green-400"
                        : "text-red-600 dark:text-red-400"
                    }`}
                  >
                    {results.mid?.upside}
                  </td>
                  <td
                    className={`px-4 py-3 text-center font-medium ${
                      results.high?.upside &&
                      parseFloat(results.high.upside.replace("%", "")) >= 0
                        ? "text-green-600 dark:text-green-400"
                        : "text-red-600 dark:text-red-400"
                    }`}
                  >
                    {results.high?.upside}
                  </td>
                </tr>
              </>
            )}
          </tbody>
        </table>
      </div>

      <div className="p-3 flex justify-center">
        <button
          onClick={onSave}
          className="w-full py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded transition-colors"
        >
          {t("valuation.saveValuation") || "Save The Valuation"}
        </button>
      </div>
    </div>
  );
};

ValuationResults.propTypes = {
  results: PropTypes.shape({
    low: PropTypes.shape({
      price: PropTypes.string.isRequired,
      upside: PropTypes.string.isRequired,
    }),
    mid: PropTypes.shape({
      price: PropTypes.string.isRequired,
      upside: PropTypes.string.isRequired,
    }),
    high: PropTypes.shape({
      price: PropTypes.string.isRequired,
      upside: PropTypes.string.isRequired,
    }),
  }).isRequired,
  currency: PropTypes.string.isRequired,
  onSave: PropTypes.func.isRequired,
  apiEstimation: PropTypes.shape({
    symbol: PropTypes.string,
    realPrice: PropTypes.number,
    estimationPrices: PropTypes.shape({
      low: PropTypes.number,
      mid: PropTypes.number,
      high: PropTypes.number,
    }),
    percentageChanges: PropTypes.shape({
      low: PropTypes.number,
      mid: PropTypes.number,
      high: PropTypes.number,
    }),
  }),
  isApiEstimation: PropTypes.bool,
};

export default ValuationResults;
