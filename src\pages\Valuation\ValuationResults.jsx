import PropTypes from "prop-types";
import { useTranslation } from "react-i18next";

const ValuationResults = ({ results, currency, onSave }) => {
  const { t } = useTranslation();

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden w-80">
      <div className="p-3 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
        <h3 className="text-sm text-center font-medium text-gray-700 dark:text-gray-300">
          {t("valuation.estimationPrice") || "My Estimation Price"}
        </h3>
      </div>

      <div className="p-0">
        <table className="w-full text-sm">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th className="px-4 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-300">
                {t("valuation.low") || "Low"}
              </th>
              <th className="px-4 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-300">
                {t("valuation.mid") || "Mid"}
              </th>
              <th className="px-4 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-300">
                {t("valuation.high") || "High"}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr className="border-b border-gray-200 dark:border-gray-700">
              <td className="px-4 py-3 text-center text-gray-900 dark:text-white">
                {results.low.price} {currency}
              </td>
              <td className="px-4 py-3 text-center text-green-500 dark:text-green-400">
                {results.mid.price} {currency}
              </td>
              <td className="px-4 py-3 text-center text-green-500 dark:text-green-400">
                {results.high.price} {currency}
              </td>
            </tr>
            <tr>
              {/* <td className="px-4 py-3 text-center text-gray-500 dark:text-gray-400"> */}
              <td className="px-4 py-3 text-center text-gray-900 dark:text-white">
                {results.low.upside}
              </td>
              <td className="px-4 py-3 text-center text-green-500 dark:text-green-400">
                {results.mid.upside}
              </td>
              <td className="px-4 py-3 text-center text-green-500 dark:text-green-400">
                {results.high.upside}
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div className="p-3 flex justify-center">
        <button
          onClick={onSave}
          className="w-full py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-white text-sm font-medium rounded border border-gray-300 dark:border-gray-600"
        >
          {t("valuation.saveValuation") || "Save The Valuation"}
        </button>
      </div>
    </div>
  );
};

ValuationResults.propTypes = {
  results: PropTypes.shape({
    low: PropTypes.shape({
      price: PropTypes.string.isRequired,
      upside: PropTypes.string.isRequired,
    }),
    mid: PropTypes.shape({
      price: PropTypes.string.isRequired,
      upside: PropTypes.string.isRequired,
    }),
    high: PropTypes.shape({
      price: PropTypes.string.isRequired,
      upside: PropTypes.string.isRequired,
    }),
  }).isRequired,
  currency: PropTypes.string.isRequired,
  onSave: PropTypes.func.isRequired,
};

export default ValuationResults;
