import PropTypes from "prop-types";
import { useTranslation } from "react-i18next";

const ValuationResults = ({
  results,
  currency,
  onSave,
  apiEstimation,
  isApiEstimation = false,
}) => {
  const { t } = useTranslation();

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden w-80">
      <div className="p-3 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
        <h3 className="text-sm text-center font-medium text-gray-700 dark:text-gray-300">
          {isApiEstimation
            ? t("valuation.apiEstimationPrice") || "API Estimation Results"
            : t("valuation.estimationPrice") || "My Estimation Price"}
        </h3>
      </div>

      <div className="p-0">
        <table className="w-full text-sm">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th className="px-4 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-300">
                {t("valuation.low") || "Low"}
              </th>
              <th className="px-4 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-300">
                {t("valuation.mid") || "Mid"}
              </th>
              <th className="px-4 py-2 text-center text-xs font-medium text-gray-500 dark:text-gray-300">
                {t("valuation.high") || "High"}
              </th>
            </tr>
          </thead>
          <tbody>
            {isApiEstimation && apiEstimation ? (
              <>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <td className="px-4 py-3 text-center text-gray-900 dark:text-white">
                    {apiEstimation.estimationPrices.low.toFixed(2)} {currency}
                  </td>
                  <td className="px-4 py-3 text-center text-gray-900 dark:text-white">
                    {apiEstimation.estimationPrices.mid.toFixed(2)} {currency}
                  </td>
                  <td className="px-4 py-3 text-center text-gray-900 dark:text-white">
                    {apiEstimation.estimationPrices.high.toFixed(2)} {currency}
                  </td>
                </tr>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <td
                    className={`px-4 py-3 text-center font-medium rounded-md mx-1 ${
                      apiEstimation.percentageChanges.low >= 0
                        ? "text-green-700 dark:text-green-300 bg-green-50 dark:bg-green-900/20"
                        : "text-red-700 dark:text-red-300 bg-red-50 dark:bg-red-900/20"
                    }`}
                  >
                    <div className="flex items-center justify-center gap-1">
                      {apiEstimation.percentageChanges.low >= 0 ? (
                        <svg
                          className="w-4 h-4"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L10 4.414 6.707 7.707a1 1 0 01-1.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      ) : (
                        <svg
                          className="w-4 h-4"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M14.707 12.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L10 15.586l3.293-3.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      )}
                      <span>
                        {apiEstimation.percentageChanges.low >= 0 ? "+" : ""}
                        {apiEstimation.percentageChanges.low.toFixed(1)}%
                      </span>
                    </div>
                  </td>
                  <td
                    className={`px-4 py-3 text-center font-medium rounded-md mx-1 ${
                      apiEstimation.percentageChanges.mid >= 0
                        ? "text-green-700 dark:text-green-300 bg-green-50 dark:bg-green-900/20"
                        : "text-red-700 dark:text-red-300 bg-red-50 dark:bg-red-900/20"
                    }`}
                  >
                    <div className="flex items-center justify-center gap-1">
                      {apiEstimation.percentageChanges.mid >= 0 ? (
                        <svg
                          className="w-4 h-4"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L10 4.414 6.707 7.707a1 1 0 01-1.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      ) : (
                        <svg
                          className="w-4 h-4"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M14.707 12.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L10 15.586l3.293-3.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      )}
                      <span>
                        {apiEstimation.percentageChanges.mid >= 0 ? "+" : ""}
                        {apiEstimation.percentageChanges.mid.toFixed(1)}%
                      </span>
                    </div>
                  </td>
                  <td
                    className={`px-4 py-3 text-center font-medium rounded-md mx-1 ${
                      apiEstimation.percentageChanges.high >= 0
                        ? "text-green-700 dark:text-green-300 bg-green-50 dark:bg-green-900/20"
                        : "text-red-700 dark:text-red-300 bg-red-50 dark:bg-red-900/20"
                    }`}
                  >
                    <div className="flex items-center justify-center gap-1">
                      {apiEstimation.percentageChanges.high >= 0 ? (
                        <svg
                          className="w-4 h-4"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L10 4.414 6.707 7.707a1 1 0 01-1.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      ) : (
                        <svg
                          className="w-4 h-4"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M14.707 12.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L10 15.586l3.293-3.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      )}
                      <span>
                        {apiEstimation.percentageChanges.high >= 0 ? "+" : ""}
                        {apiEstimation.percentageChanges.high.toFixed(1)}%
                      </span>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td
                    colSpan="3"
                    className="px-4 py-2 text-center text-sm text-gray-600 dark:text-gray-400"
                  >
                    {t("valuation.realPrice") || "Real Price"}:{" "}
                    {apiEstimation.realPrice.toFixed(2)} {currency}
                  </td>
                </tr>
              </>
            ) : (
              <>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <td className="px-4 py-3 text-center text-gray-900 dark:text-white">
                    {results.low?.price} {currency}
                  </td>
                  <td className="px-4 py-3 text-center text-gray-900 dark:text-white">
                    {results.mid?.price} {currency}
                  </td>
                  <td className="px-4 py-3 text-center text-gray-900 dark:text-white">
                    {results.high?.price} {currency}
                  </td>
                </tr>
                <tr>
                  <td
                    className={`px-4 py-3 text-center font-medium rounded-md mx-1 ${
                      results.low?.upside &&
                      parseFloat(results.low.upside.replace("%", "")) >= 0
                        ? "text-green-700 dark:text-green-300 bg-green-50 dark:bg-green-900/20"
                        : "text-red-700 dark:text-red-300 bg-red-50 dark:bg-red-900/20"
                    }`}
                  >
                    <div className="flex items-center justify-center gap-1">
                      {results.low?.upside &&
                      parseFloat(results.low.upside.replace("%", "")) >= 0 ? (
                        <svg
                          className="w-4 h-4"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L10 4.414 6.707 7.707a1 1 0 01-1.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      ) : (
                        <svg
                          className="w-4 h-4"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M14.707 12.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L10 15.586l3.293-3.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      )}
                      <span>{results.low?.upside}</span>
                    </div>
                  </td>
                  <td
                    className={`px-4 py-3 text-center font-medium rounded-md mx-1 ${
                      results.mid?.upside &&
                      parseFloat(results.mid.upside.replace("%", "")) >= 0
                        ? "text-green-700 dark:text-green-300 bg-green-50 dark:bg-green-900/20"
                        : "text-red-700 dark:text-red-300 bg-red-50 dark:bg-red-900/20"
                    }`}
                  >
                    <div className="flex items-center justify-center gap-1">
                      {results.mid?.upside &&
                      parseFloat(results.mid.upside.replace("%", "")) >= 0 ? (
                        <svg
                          className="w-4 h-4"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L10 4.414 6.707 7.707a1 1 0 01-1.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      ) : (
                        <svg
                          className="w-4 h-4"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M14.707 12.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L10 15.586l3.293-3.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      )}
                      <span>{results.mid?.upside}</span>
                    </div>
                  </td>
                  <td
                    className={`px-4 py-3 text-center font-medium rounded-md mx-1 ${
                      results.high?.upside &&
                      parseFloat(results.high.upside.replace("%", "")) >= 0
                        ? "text-green-700 dark:text-green-300 bg-green-50 dark:bg-green-900/20"
                        : "text-red-700 dark:text-red-300 bg-red-50 dark:bg-red-900/20"
                    }`}
                  >
                    <div className="flex items-center justify-center gap-1">
                      {results.high?.upside &&
                      parseFloat(results.high.upside.replace("%", "")) >= 0 ? (
                        <svg
                          className="w-4 h-4"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M5.293 7.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L10 4.414 6.707 7.707a1 1 0 01-1.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      ) : (
                        <svg
                          className="w-4 h-4"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M14.707 12.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L10 15.586l3.293-3.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      )}
                      <span>{results.high?.upside}</span>
                    </div>
                  </td>
                </tr>
              </>
            )}
          </tbody>
        </table>
      </div>

      <div className="p-3 flex justify-center">
        <button
          onClick={onSave}
          className="w-full py-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-white text-sm font-medium rounded border border-gray-300 dark:border-gray-600"
        >
          {t("valuation.saveValuation") || "Save The Valuation"}
        </button>
      </div>
    </div>
  );
};

ValuationResults.propTypes = {
  results: PropTypes.shape({
    low: PropTypes.shape({
      price: PropTypes.string.isRequired,
      upside: PropTypes.string.isRequired,
    }),
    mid: PropTypes.shape({
      price: PropTypes.string.isRequired,
      upside: PropTypes.string.isRequired,
    }),
    high: PropTypes.shape({
      price: PropTypes.string.isRequired,
      upside: PropTypes.string.isRequired,
    }),
  }).isRequired,
  currency: PropTypes.string.isRequired,
  onSave: PropTypes.func.isRequired,
  apiEstimation: PropTypes.shape({
    symbol: PropTypes.string,
    realPrice: PropTypes.number,
    estimationPrices: PropTypes.shape({
      low: PropTypes.number,
      mid: PropTypes.number,
      high: PropTypes.number,
    }),
    percentageChanges: PropTypes.shape({
      low: PropTypes.number,
      mid: PropTypes.number,
      high: PropTypes.number,
    }),
  }),
  isApiEstimation: PropTypes.bool,
};

export default ValuationResults;
