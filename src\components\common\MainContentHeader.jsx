import { useLocation } from "react-router-dom";
import StockIndexChart from "./StockIndexChart";

const MainContentHeader = () => {
  const location = useLocation();
  console.log("Current Path:", location.pathname);
  const getPageName = () => {
    switch (location.pathname) {
      case "/stocks":
        return "Stocks";
      case "/watchlist":
        return "Watchlist";
      case "/valuation":
        return "Valuation";
      case "/stock-screener":
        return "Stock Screener";
      case "/news":
        return "News";  
      case "/preliminary-financial-results":
        return "Preliminary Financial Results";
      case "/institute-of-research-and-study":
        return "Institute Of Research And Study";
      case "/chart":
        return "Charts";
      case "/virtual-portfolio":
        return "Virtual Portfolio";
      case "/store":
        return "Store";
      default:
        return "Dashboard";
    }
  };
  return (
    <div className="flex flex-wrap items-center justify-between mx-auto p-4">
      <h1 className="self-center text-2xl font-semibold whitespace-nowrap dark:text-white">
        {getPageName()}
      </h1>
      <StockIndexChart/>
    </div>
  );
};

export default MainContentHeader;
