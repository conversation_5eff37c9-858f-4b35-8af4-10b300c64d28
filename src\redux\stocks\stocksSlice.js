import { createSlice } from "@reduxjs/toolkit";
import {
  fetchStocks,
  fetchStockHeaderBySymbol,
  getTimeSeriesData,
  fetchQuotesBySymbols,
  fetchMarketStocks,     // ← CHANGED
  fetchIndustriesList,   // ← CHANGED
  fetchByIndustry,       // ← CHANGED
} from "./stocksActions";

const initialState = {
  data: [],             // Current page’s array of { symbol, data: { … } }
  totalPages: 0,        // ← CHANGED: how many pages exist (for 10 items each)
  header: null,         // result of fetchStockHeaderBySymbol
  timeSeriesData: null, // result of getTimeSeriesData
  quotes: [],           // result of fetchQuotesBySymbols
  status: "idle",       // “idle” | “loading” | “succeeded” | “failed”
  error: null,
  industries: [],       // Array of industry strings
  currentIndex: "All Market", // Tracks which index was last fetched
};

const stocksSlice = createSlice({
  name: "stocks",
  initialState,
  reducers: {
    // Optionally reset the state
    resetStocksState(state) {
      state.data = [];
      state.totalPages = 0;             // ← CHANGED
      state.status = "idle";
      state.error = null;
      state.currentIndex = "All Market";
    },
    setCurrentIndex(state, action) {
      state.currentIndex = action.payload;
    },
  },
  extraReducers: (builder) => {
    //
    // 1) Master list from external API (unused for paginated view)
    //
    builder
      .addCase(fetchStocks.pending, (state) => {
        state.status = "loading";
        state.error = null;
      })
      .addCase(fetchStocks.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.data = action.payload;
      })
      .addCase(fetchStocks.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.error.message;
      });

    //
    // 2) Header details
    //
    builder
      .addCase(fetchStockHeaderBySymbol.pending, (state) => {
        state.status = "loading";
        state.error = null;
      })
      .addCase(fetchStockHeaderBySymbol.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.header = action.payload;
      })
      .addCase(fetchStockHeaderBySymbol.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload;
      });

    //
    // 3) Time series data
    //
    builder
      .addCase(getTimeSeriesData.pending, (state) => {
        state.status = "loading";
        state.error = null;
      })
      .addCase(getTimeSeriesData.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.timeSeriesData = action.payload;
      })
      .addCase(getTimeSeriesData.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload;
      });

    //
    // 4) Live quotes
    //
    builder
      .addCase(fetchQuotesBySymbols.pending, (state) => {
        state.status = "loading";
        state.error = null;
      })
      .addCase(fetchQuotesBySymbols.fulfilled, (state, action) => {
        state.status = "succeeded";
        // Merge each quote into the existing state.data entry
        action.payload.forEach((q) => {
          const idx = state.data.findIndex((s) => s.symbol === q.symbol);
          if (idx !== -1) {
            state.data[idx] = { ...state.data[idx], ...q };
          }
        });
        state.quotes = action.payload;
      })
      .addCase(fetchQuotesBySymbols.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload;
      });


    // 8) fetchMarketStocks ← **This is now paginated (10 per page)**  
    //    payloadCreator returns { data: [ … ], totalPages: <number> }
    //
    builder
      .addCase(fetchMarketStocks.pending, (state) => {
        state.status = "loading";
        state.error = null;
      })
      .addCase(fetchMarketStocks.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.data = action.payload.data;             // up to 10 symbols
        state.totalPages = action.payload.totalPages; // how many pages in total
      })
      .addCase(fetchMarketStocks.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload || action.error.message;
      });

    //
    // 9) fetchIndustriesList
    //
    builder
      .addCase(fetchIndustriesList.pending, (state) => {
        state.status = "loading";
        state.error = null;
      })
      .addCase(fetchIndustriesList.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.industries = action.payload; // array of strings
      })
      .addCase(fetchIndustriesList.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload || action.error.message;
      });

    //
    // 10) fetchByIndustry (no pagination here)
    //
    builder
      .addCase(fetchByIndustry.pending, (state) => {
        state.status = "loading";
        state.error = null;
      })
      .addCase(fetchByIndustry.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.data = action.payload; // array of { symbol, data: { … } }
        // We do not set totalPages here because this is a non-paginated “filter by industry” call
      })
      .addCase(fetchByIndustry.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload || action.error.message;
      });
  },
});

export const { resetStocksState, setCurrentIndex } = stocksSlice.actions;
export default stocksSlice.reducer;
