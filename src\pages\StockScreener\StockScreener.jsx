import React, { useEffect } from "react"; // Removed useState as local states are lifted
import { useDispatch, useSelector } from "react-redux";
import { Tabs, Pagination } from "antd";
// REMOVED: MarketSelector import
import Overview from "./Tabs/Overview";
import Performance from "./Tabs/Performance";
import Earnings from "./Tabs/Earnings";
import Filters from "./Tabs/Filters"; // Adjust the path as needed
import {
  loadScreenerOverview,
  loadPerformance,
  loadEarnings,
  loadFilterTab
} from "../../redux/screener/screenerSlice"; // Ensure path is correct

const { TabPane } = Tabs;

function normalizeMarket(raw) {
  if (!raw) return "All Market"; // Fallback
  switch (raw.toLowerCase()) {
    case "tasi":
      return "TASI";
    case "numo":
      return "NUMO";
    case "all":
      return "All Market";
    default:
      return raw.toUpperCase(); // Ensure consistent casing if other values possible
  }
}

const StockScreener = ({
  selectedMarket, // From Index.jsx
  currentPage,    // From Index.jsx
  setCurrentPage, // From Index.jsx
  activeTab,      // From Index.jsx
  setActiveTab,   // From Index.jsx
  // Formatted filter params from Index.jsx
  filterMarketCap,
  filterIndustry,
  filterStockPrice,
}) => {
  const dispatch = useDispatch();
  const { overview, performance, earnings, filters: filtersData, status } // Renamed filters to filtersData
    = useSelector((state) => state.screener);
  // selectedFilters from a different Redux slice, used for the "Filters" tab's specific indicators
  const selectedFiltersFromRedux = useSelector(state => state.filters?.selectedFilters);

  // REMOVED: useEffect for localStorage and local setCurrentPage(1) on selectedMarketRaw change.
  // This is handled by Index.jsx.

  // Central data fetching useEffect
  useEffect(() => {
    const apiIndex = normalizeMarket(selectedMarket);
    const params = {
      page: Number(currentPage),
      index: apiIndex,
      "Market Capitalization": filterMarketCap, // Use formatted prop
      "Industry": filterIndustry,             // Use formatted prop
      "Share Price": filterStockPrice,        // Use formatted prop
    };

    // Dispatch only the API for the active tab
    if (activeTab === "overview") {
      dispatch(loadScreenerOverview(params));
    } else if (activeTab === "performance") {
      dispatch(loadPerformance(params));
    } else if (activeTab === "earnings") {
      dispatch(loadEarnings(params));
    } else if (activeTab === "filters") {
      dispatch(loadFilterTab({
        ...params,
        indicators: selectedFiltersFromRedux || [] // Include selected filters if available
      }));
    }
  }, [
    selectedMarket,
    currentPage,
    activeTab,
    filterMarketCap,
    filterIndustry,
    filterStockPrice,
    selectedFiltersFromRedux, // Add if the 'filters' tab relies on this
    dispatch,
  ]);

  const handleTabChange = (key) => {
    setActiveTab(key);  // Uses setter from props
    setCurrentPage(1);  // Reset page on tab change, uses setter from props
  };

  const handlePageChange = (page) => {
    setCurrentPage(page); // Uses setter from props
  };

  // Determine total pages based on the active tab's data
  const totalPagesForPagination =
    activeTab === "overview"
      ? overview.totalPages
      : activeTab === "performance"
      ? performance.totalPages
      : activeTab === "earnings"
      ? earnings.totalPages
      : activeTab === "filters"
      ? filtersData.totalPages // Use filtersData
      : 0;

  const renderTabContent = (tabKey, data, Component) => {
    if (activeTab !== tabKey) return null; // Render only active tab content
    if (status === "loading") return <p>Loading {tabKey.charAt(0).toUpperCase() + tabKey.slice(1)}...</p>;
    if (status === "failed") return <p>Error loading data for {tabKey}.</p>;
    return <Component data={data} />;
  };

  return (
    <div className="space-y-4">
      {/* REMOVED: Local MarketSelector component */}
      <Tabs activeKey={activeTab} onChange={handleTabChange}>
        <TabPane tab="Overview" key="overview">
          {renderTabContent("overview", overview.data, Overview)}
        </TabPane>
        <TabPane tab="Performance" key="performance">
          {renderTabContent("performance", performance.data, Performance)}
        </TabPane>
        <TabPane tab="Earnings" key="earnings">
          {renderTabContent("earnings", earnings.data, Earnings)}
        </TabPane>
        <TabPane tab="Filters" key="filters">
          {renderTabContent("filters", filtersData.data, Filters)}
        </TabPane>
      </Tabs>
      {totalPagesForPagination > 0 && (
         <Pagination
            current={currentPage}
            total={totalPagesForPagination * 10} // Assuming 10 items per page is fixed or comes from API
            pageSize={10} // This should ideally match backend or be configurable
            onChange={handlePageChange}
            showSizeChanger={false} // Keep false if pageSize is fixed
            className="mt-4 text-center"
         />
      )}
    </div>
  );
};

export default React.memo(StockScreener); // Memoize StockScreener