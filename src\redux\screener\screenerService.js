import axios from 'axios';
const backendURL = import.meta.env.VITE_BACKEND_URL;

export const fetchStockScreenerOverview = async (params) => {
  const res = await axios.post(`${backendURL}/screener/stocks-screener-overview`, params);
  return res.data.response;
};

export const fetchPerformance = async (params) => {
  const res = await axios.post(`${backendURL}/screener/stocks-screener-performance`, params);
  return res.data.response;
};

export const fetchEarnings = async (params) => {
  const res = await axios.post(`${backendURL}/screener/stocks-screener-earnings`, params);
  return res.data.response;
};

export const fetchFilterTab = async (params) => {
  const res = await axios.post(`${backendURL}/screener/stocks-screener-filter-tab`, params);
  return res.data.response;
};