// import React from "react";
// import StockDetailsCard from "../../components/tabs/OverviewTab/StockDetailsCard";
//  import StockChart from "../../components/tabs/OverviewTab/StockChart";


// const ReportsDetailsPage = () => {
//   return (
//     <div className="p-4 space-y-4 text-[12px]">
//       {/* Top Summary */}
//       <div className="text-center font-semibold">
//         Revenue +16.7% (SAR +377.5 Million) To reach SAR 2,635 Million and Net Profit ****% (SAR +22.3 Million) To reach SAR 255.2 Million.
//       </div>

//       {/* Table */}
//       <div className="overflow-x-auto border border-black">
//         <table className="w-full text-center border-collapse">
//           <thead className="bg-gray-200 text-[11px]">
//             <tr>
//               <th className="border border-black p-1">Item</th>
//               <th className="border border-black p-1">Quarter 2025</th>
//               <th className="border border-black p-1">Similar Quarter 2024</th>
//               <th className="border border-black p-1">Previous Quarter 2025</th>
//               <th className="border border-black p-1">Change YOY</th>
//               <th className="border border-black p-1">Change QOQ</th>
//             </tr>
//           </thead>
//           <tbody className="text-[11px]">
//             <tr>
//               <td className="border border-black p-1">Sales/Revenue</td>
//               <td className="border border-black p-1">2,635</td>
//               <td className="border border-black p-1">2,257.5</td>
//               <td className="border border-black p-1">2,363</td>
//               <td className="border border-black p-1">+16.7%</td>
//               <td className="border border-black p-1">+11.5%</td>
//             </tr>
//             <tr>
//               <td className="border border-black p-1">Cost of Revenue</td>
//               <td className="border border-black p-1">1,684.1</td>
//               <td className="border border-black p-1">1,396.7</td>
//               <td className="border border-black p-1">1,480.6</td>
//               <td className="border border-black p-1">+20.6%</td>
//               <td className="border border-black p-1">+13.7%</td>
//             </tr>  
//             <tr>
//               <td className="border border-black p-1">Gross Profit</td>
//               <td className="border border-black p-1">950.9</td>
//               <td className="border border-black p-1">860.8</td>
//               <td className="border border-black p-1">882.4</td>
//               <td className="border border-black p-1">+10.5%</td>
//               <td className="border border-black p-1">****%</td>
//             </tr>
//             <tr>
//               <td className="border border-black p-1">Operating Profit</td>
//               <td className="border border-black p-1">270</td>
//               <td className="border border-black p-1">231.6</td>
//               <td className="border border-black p-1">192.3</td>
//               <td className="border border-black p-1">+16.6%</td>
//               <td className="border border-black p-1">+40.4%</td>
//             </tr>
//             <tr>
//               <td className="border border-black p-1">Net Profit (Loss)</td>
//               <td className="border border-black p-1">255.2</td>
//               <td className="border border-black p-1">232.9</td>
//               <td className="border border-black p-1">157.9</td>
//               <td className="border border-black p-1">****%</td>
//               <td className="border border-black p-1">+61.6%</td>
//             </tr>
//             <tr>
//               <td className="border border-black p-1">Total Comprehensive Income</td>
//               <td className="border border-black p-1">252.5</td>
//               <td className="border border-black p-1">244.2</td>
//               <td className="border border-black p-1">177</td>
//               <td className="border border-black p-1">****%</td>
//               <td className="border border-black p-1">+42.9%</td>
//             </tr>
//             <tr>
//               <td className="border border-black p-1">Non-Operational Costs</td>
//               <td className="border border-black p-1">18.4</td>
//               <td className="border border-black p-1">13.8</td>
//               <td className="border border-black p-1">-3.4</td>
//               <td className="border border-black p-1">+33.3%</td>
//               <td className="border border-black p-1">—</td>
//             </tr>
//           </tbody>
//         </table>
//       </div>

//       {/* Breakdown Text */}
//       <div className="text-[12px] space-y-2">
//         <p><strong>Revenue:</strong> Sales/Revenue experienced a significant year-over-year increase of +16.7%, rising from SAR 2,257.5 Million in the similar quarter of the previous year to SAR 2,635 Million in the current quarter. On a quarter-over-quarter basis, revenue also showed a positive growth of +11.5% compared to the previous quarter's SAR 2,363 Million.</p>
//         <p><strong>Cost of Revenue:</strong> The Cost of Revenue increased by +20.6% year-over-year, from SAR 1,396.7 Million to SAR 1,684.1 Million. Quarter-over-quarter, the Cost of Revenue also rose by +13.7%, from SAR 1,480.6 Million.</p>
//         <p><strong>Gross Profit:</strong> Gross Profit saw a year-over-year increase of +10.5%, climbing from SAR 860.8 Million to SAR 950.9 Million. The quarter-over-quarter growth in Gross Profit was ****%, increasing from SAR 882.4 Million.</p>
//         <p><strong>Operating Income (Operating Profit):</strong> Operating Profit demonstrated a strong year-over-year growth of +16.6%, increasing from SAR 231.6 Million to SAR 270 Million. The quarter-over-quarter increase was even more substantial at +40.4%, rising from SAR 192.3 Million.</p>
//         <p><strong>Net Income (Net Profit):</strong> Net Profit recorded a year-over-year increase of ****%, growing from SAR 232.9 Million to SAR 255.2 Million. There was a significant quarter-over-quarter surge of +61.6% in Net Profit, up from SAR 157.9 Million.</p>
//         <p><strong>Total Comprehensive Income:</strong> Total Comprehensive Income showed a modest year-over-year increase of ****%, from SAR 244.2 Million to SAR 252.5 Million. The quarter-over-quarter growth was significant at +42.9%, rising from SAR 177 Million.</p>
//         <p><strong>Non-Operational Costs:</strong> The Non-Operational Costs switched from a positive SAR 13.8 Million in the similar quarter of the previous year to a negative SAR 18.4 Million in the current quarter, and from a negative SAR 3.4 Million in the previous quarter to a negative SAR 18.4 Million in the current quarter. Therefore, the percentage change for this item is not defined.</p>
//       </div>
//   {/* <StockChart chartData={chartData} />
//       <StockDetailsCard /> */}
     
//     </div>
//   );
// };

// export default ReportsDetailsPage;






// import React from "react";
// import StockDetailsCard from "../../components/tabs/OverviewTab/StockDetailsCard";
//  import StockChart from "../../components/tabs/OverviewTab/StockChart";

// const ReportsDetailsPage = () => {
  
//   return (
//     <div className="p-6 space-y-6 text-sm font-[Inter] bg-gray-50 min-h-screen">
//       {/* Summary Section */}
//       <div className="bg-white text-center text-[14px] font-semibold border border-gray-300 rounded-lg px-6 py-4 shadow-sm">
//         Revenue <span className="text-green-600">+16.7%</span> (SAR +377.5 Million) to reach <strong>SAR 2,635 Million</strong> and Net Profit <span className="text-green-600">****%</span> (SAR +22.3 Million) to reach <strong>SAR 255.2 Million</strong>.
//       </div>

//       {/* Table Section */}
//       <div className="overflow-x-auto rounded-lg border border-gray-300 shadow-sm">
//         <table className="w-full border-collapse text-center text-[12px]">
//           <thead className="bg-gray-100 text-gray-700">
//             <tr>
//               <th className="border border-gray-300 p-2">Item</th>
//               <th className="border border-gray-300 p-2">Quarter 2025</th>
//               <th className="border border-gray-300 p-2">Similar Quarter 2024</th>
//               <th className="border border-gray-300 p-2">Previous Quarter 2025</th>
//               <th className="border border-gray-300 p-2">Change YOY</th>
//               <th className="border border-gray-300 p-2">Change QOQ</th>
//             </tr>
//           </thead>
//           <tbody className="text-gray-800">
//             {[
//               ["Sales/Revenue", "2,635", "2,257.5", "2,363", "+16.7%", "+11.5%"],
//               ["Cost of Revenue", "1,684.1", "1,396.7", "1,480.6", "+20.6%", "+13.7%"],
//               ["Gross Profit", "950.9", "860.8", "882.4", "+10.5%", "****%"],
//               ["Operating Profit", "270", "231.6", "192.3", "+16.6%", "+40.4%"],
//               ["Net Profit (Loss)", "255.2", "232.9", "157.9", "****%", "+61.6%"],
//               ["Total Comprehensive Income", "252.5", "244.2", "177", "****%", "+42.9%"],
//               ["Non-Operational Costs", "18.4", "13.8", "-3.4", "+33.3%", "—"],
//             ].map((row, rowIndex) => (
//               <tr key={rowIndex} className={rowIndex % 2 === 0 ? "bg-white" : "bg-gray-50"}>
//                 {row.map((cell, colIndex) => (
//                   <td key={colIndex} className="border border-gray-300 p-2">{cell}</td>
//                 ))}
//               </tr>
//             ))}
//           </tbody>
//         </table>
//       </div>

//       {/* Breakdown Section */}
//       <div className="bg-white p-5 rounded-lg border border-gray-300 shadow-sm space-y-4 text-[13px] leading-relaxed text-gray-800">
//         <p><strong>Revenue:</strong> Sales/Revenue experienced a significant year-over-year increase of +16.7%, rising from SAR 2,257.5 Million in the similar quarter of the previous year to SAR 2,635 Million in the current quarter. On a quarter-over-quarter basis, revenue also showed a positive growth of +11.5% compared to the previous quarter's SAR 2,363 Million.</p>
//         <p><strong>Cost of Revenue:</strong> The Cost of Revenue increased by +20.6% year-over-year, from SAR 1,396.7 Million to SAR 1,684.1 Million. Quarter-over-quarter, the Cost of Revenue also rose by +13.7%, from SAR 1,480.6 Million.</p>
//         <p><strong>Gross Profit:</strong> Gross Profit saw a year-over-year increase of +10.5%, climbing from SAR 860.8 Million to SAR 950.9 Million. The quarter-over-quarter growth in Gross Profit was ****%, increasing from SAR 882.4 Million.</p>
//         <p><strong>Operating Income (Operating Profit):</strong> Operating Profit demonstrated a strong year-over-year growth of +16.6%, increasing from SAR 231.6 Million to SAR 270 Million. The quarter-over-quarter increase was even more substantial at +40.4%, rising from SAR 192.3 Million.</p>
//         <p><strong>Net Income (Net Profit):</strong> Net Profit recorded a year-over-year increase of ****%, growing from SAR 232.9 Million to SAR 255.2 Million. There was a significant quarter-over-quarter surge of +61.6% in Net Profit, up from SAR 157.9 Million.</p>
//         <p><strong>Total Comprehensive Income:</strong> Total Comprehensive Income showed a modest year-over-year increase of ****%, from SAR 244.2 Million to SAR 252.5 Million. The quarter-over-quarter growth was significant at +42.9%, rising from SAR 177 Million.</p>
//         <p><strong>Non-Operational Costs:</strong> The Non-Operational Costs switched from a positive SAR 13.8 Million in the similar quarter of the previous year to a negative SAR 18.4 Million in the current quarter, and from a negative SAR 3.4 Million in the previous quarter to a negative SAR 18.4 Million in the current quarter. Therefore, the percentage change for this item is not defined.</p>
//       </div>

//    // Only render if you have chartData
// {chartData && <StockChart chartData={chartData} />}
//       <StockDetailsCard />
//     </div>
//   );
// };

// export default ReportsDetailsPage;






// import React from "react";
// import OverviewTab from "../../components/tabs/OverviewTab/OverviewTab";

// const ReportsDetailsPage = () => {
//   return (
//     <div className="p-6 space-y-6 text-sm font-[Inter] bg-gray-50 min-h-screen">
//       {/* Summary Section */}
//       <div className="bg-white text-center text-[14px] font-semibold border border-gray-300 rounded-lg px-6 py-4 shadow-sm">
//         Revenue <span className="text-green-600">+16.7%</span> (SAR +377.5 Million) to reach <strong>SAR 2,635 Million</strong> and Net Profit <span className="text-green-600">****%</span> (SAR +22.3 Million) to reach <strong>SAR 255.2 Million</strong>.
//       </div>

//       {/* Table and Overview Side by Side */}
//       <div className="flex flex-col lg:flex-row gap-4">
//         {/* Financial Table */}
//         <div className="flex-1 overflow-x-auto rounded-lg border border-gray-300 shadow-sm bg-white">
//           <table className="w-full border-collapse text-center text-[12px]">
//             <thead className="bg-gray-100 text-gray-700">
//               <tr>
//                 <th className="border border-gray-300 p-2">Item</th>
//                 <th className="border border-gray-300 p-2">Quarter 2025</th>
//                 <th className="border border-gray-300 p-2">Similar Quarter 2024</th>
//                 <th className="border border-gray-300 p-2">Previous Quarter 2025</th>
//                 <th className="border border-gray-300 p-2">Change YOY</th>
//                 <th className="border border-gray-300 p-2">Change QOQ</th>
//               </tr>
//             </thead>
//             <tbody className="text-gray-800">
//               {[
//                 ["Sales/Revenue", "2,635", "2,257.5", "2,363", "+16.7%", "+11.5%"],
//                 ["Cost of Revenue", "1,684.1", "1,396.7", "1,480.6", "+20.6%", "+13.7%"],
//                 ["Gross Profit", "950.9", "860.8", "882.4", "+10.5%", "****%"],
//                 ["Operating Profit", "270", "231.6", "192.3", "+16.6%", "+40.4%"],
//                 ["Net Profit (Loss)", "255.2", "232.9", "157.9", "****%", "+61.6%"],
//                 ["Total Comprehensive Income", "252.5", "244.2", "177", "****%", "+42.9%"],
//                 ["Non-Operational Costs", "18.4", "13.8", "-3.4", "+33.3%", "—"],
//               ].map((row, rowIndex) => (
//                 <tr key={rowIndex} className={rowIndex % 2 === 0 ? "bg-white" : "bg-gray-50"}>
//                   {row.map((cell, colIndex) => (
//                     <td key={colIndex} className="border border-gray-300 p-2">{cell}</td>
//                   ))}
//                 </tr>
//               ))}
//             </tbody>
//           </table>
//         </div>

//         {/* Overview Chart */}
//         <div className="lg:w-1/3 min-w-[300px] bg-white rounded-lg border border-gray-300 shadow-sm p-4">
//           <OverviewTab />
//         </div>
//       </div>

//       {/* Breakdown Section */}
//       <div className="bg-white p-5 rounded-lg border border-gray-300 shadow-sm space-y-4 text-[13px] leading-relaxed text-gray-800">
//         <p><strong>Revenue:</strong> Sales/Revenue experienced a significant year-over-year increase of +16.7%, rising from SAR 2,257.5 Million to SAR 2,635 Million.</p>
//         <p><strong>Cost of Revenue:</strong> The Cost of Revenue increased by +20.6% YOY and +13.7% QOQ.</p>
//         <p><strong>Gross Profit:</strong> Up by +10.5% YOY and ****% QOQ.</p>
//         <p><strong>Operating Income:</strong> Operating Profit rose +16.6% YOY and +40.4% QOQ.</p>
//         <p><strong>Net Income:</strong> Net Profit up ****% YOY and +61.6% QOQ.</p>
//         <p><strong>Total Comprehensive Income:</strong> Gained ****% YOY and +42.9% QOQ.</p>
//         <p><strong>Non-Operational Costs:</strong> Negative in the current quarter; percentage change undefined.</p>
//       </div>
//     </div>
//   );
// };

// export default ReportsDetailsPage;





// import React from "react";
// import OverviewTab from "../../components/tabs/OverviewTab/OverviewTab";

// const ReportsDetailsPage = () => {
//   return (
//     <div className="p-6 space-y-6 bg-white min-h-screen text-[13px] font-[Inter] text-gray-800">
//       {/* Summary Statement */}
//       <div className="text-center text-[14px] font-semibold border border-gray-300 p-3 rounded-md bg-gray-50">
//         Revenue <span className="text-green-600">+16.7%</span> (SAR +377.5 Million) to reach <strong>SAR 2,635 Million</strong> and Net Profit <span className="text-green-600">****%</span> (SAR +22.3 Million) to reach <strong>SAR 255.2 Million</strong>.
//       </div>

//       {/* Table and Overview Section Side-by-Side */}
//       <div className="flex flex-col lg:flex-row gap-4">
//         {/* Financial Table */}
//         <div className="lg:w-2/3 w-full overflow-x-auto border border-gray-300 rounded-md">
//           <table className="w-full border-collapse text-center text-sm">
//             <thead className="bg-gray-100">
//               <tr>
//                 <th className="border border-gray-300 px-2 py-1">Item</th>
//                 <th className="border border-gray-300 px-2 py-1">Quarter 2025</th>
//                 <th className="border border-gray-300 px-2 py-1">Similar Quarter 2024</th>
//                 <th className="border border-gray-300 px-2 py-1">Previous Quarter 2025</th>
//                 <th className="border border-gray-300 px-2 py-1">Change YOY</th>
//                 <th className="border border-gray-300 px-2 py-1">Change QOQ</th>
//               </tr>
//             </thead>
//             <tbody>
//               {[
//                 ["Sales/Revenue", "2,635", "2,257.5", "2,363", "+16.7%", "+11.5%"],
//                 ["Cost of Revenue", "1,684.1", "1,396.7", "1,480.6", "+20.6%", "+13.7%"],
//                 ["Gross Profit", "950.9", "860.8", "882.4", "+10.5%", "****%"],
//                 ["Operating Profit", "270", "231.6", "192.3", "+16.6%", "+40.4%"],
//                 ["Net Profit (Loss)", "255.2", "232.9", "157.9", "****%", "+61.6%"],
//                 ["Total Comprehensive Income", "252.5", "244.2", "177", "****%", "+42.9%"],
//                 ["Non-Operational Costs", "18.4", "13.8", "-3.4", "+33.3%", "—"],
//               ].map((row, idx) => (
//                 <tr key={idx} className={idx % 2 === 0 ? "bg-white" : "bg-gray-50"}>
//                   {row.map((cell, i) => (
//                     <td key={i} className="border border-gray-300 px-2 py-1">{cell}</td>
//                   ))}
//                 </tr>
//               ))}
//             </tbody>
//           </table>
//         </div>

//         {/* Overview Chart and Ratios Card */}
//         <div className="lg:w-1/3 w-full border border-gray-300 rounded-md p-3 shadow-sm bg-white">
//           <OverviewTab />
//         </div>
//       </div>

//       {/* Financial Breakdown Summary */}
//       <div className="space-y-3 leading-relaxed">
//         <p><strong>Revenue:</strong> Sales/Revenue experienced a significant year-over-year increase of +16.7%, rising from SAR 2,257.5 Million to SAR 2,635 Million. Quarter-over-quarter growth was +11.5% compared to SAR 2,363 Million.</p>
//         <p><strong>Cost of Revenue:</strong> Increased by +20.6% YOY (SAR 1,684.1M vs. SAR 1,396.7M), and +13.7% QOQ.</p>
//         <p><strong>Gross Profit:</strong> YOY growth of +10.5% and QOQ growth of ****%.</p>
//         <p><strong>Operating Income (Operating Profit):</strong> YOY +16.6% and QOQ +40.4%.</p>
//         <p><strong>Net Income (Net Profit):</strong> YOY ****%, QOQ +61.6%.</p>
//         <p><strong>Total Comprehensive Income:</strong> YOY ****%, QOQ +42.9%.</p>
//         <p><strong>Non-Operational Costs:</strong> Flipped from SAR -3.4M in previous quarter to SAR 18.4M; % change is undefined.</p>
//       </div>
//     </div>
//   );
// };

// export default ReportsDetailsPage;




// import React from "react";
// import OverviewTab from "../../components/tabs/OverviewTab/OverviewTab";
// // import StockChart from "../../components/tabs/OverviewTab/StockChart";


// const ReportsDetailsPage = () => {
//   return (
//     <div className="p-4 text-[12px] space-y-4">
//       {/* Top Summary */}
//       <div className="text-center font-semibold border border-gray-300 bg-gray-50 p-2 rounded">
//         Revenue <span className="text-green-600">+16.7%</span> (SAR +377.5 Million) To reach <strong>SAR 2,635 Million</strong> and Net Profit <span className="text-green-600">****%</span> (SAR +22.3 Million) To reach <strong>SAR 255.2 Million</strong>.
//       </div>

//       {/* Table + Side Panel */}
//       <div className="flex flex-col lg:flex-row gap-4">
//         {/* Table */}
//         <div className="lg:w-2/3 w-full overflow-x-auto border border-black rounded-md">
//           <table className="w-full text-center border-collapse">
//             <thead className="bg-gray-200 text-[11px]">
//               <tr>
//                 <th className="border border-black p-1">Item</th>
//                 <th className="border border-black p-1">Quarter 2025</th>
//                 <th className="border border-black p-1">Similar Quarter 2024</th>
//                 <th className="border border-black p-1">Previous Quarter 2025</th>
//                 <th className="border border-black p-1">Change YOY</th>
//                 <th className="border border-black p-1">Change QOQ</th>
//               </tr>
//             </thead>
//             <tbody className="text-[11px]">
//               {[
//                 ["Sales/Revenue", "2,635", "2,257.5", "2,363", "+16.7%", "+11.5%"],
//                 ["Cost of Revenue", "1,684.1", "1,396.7", "1,480.6", "+20.6%", "+13.7%"],
//                 ["Gross Profit", "950.9", "860.8", "882.4", "+10.5%", "****%"],
//                 ["Operating Profit", "270", "231.6", "192.3", "+16.6%", "+40.4%"],
//                 ["Net Profit (Loss)", "255.2", "232.9", "157.9", "****%", "+61.6%"],
//                 ["Total Comprehensive Income", "252.5", "244.2", "177", "****%", "+42.9%"],
//                 ["Non-Operational Costs", "18.4", "13.8", "-3.4", "+33.3%", "—"],
//               ].map((row, i) => (
//                 <tr key={i} className={i % 2 === 0 ? "bg-white" : "bg-gray-50"}>
//                   {row.map((cell, j) => (
//                     <td key={j} className="border border-black p-1">{cell}</td>
//                   ))}
//                 </tr>
//               ))}
//             </tbody>
//           </table>
//         </div>

//         {/* Chart + Overview */}
//         <div className="lg:w-1/3 w-full flex flex-col gap-4">
//           {/* <div className="border border-gray-300 rounded-md p-2 shadow bg-white">
//              <StockChart /> 
//           </div> */}
//           <div className="border border-gray-300 rounded-md p-2 shadow bg-white">
//             <OverviewTab />
//           </div>
//         </div>
//       </div>

//       {/* Breakdown Text */}
//       <div className="text-[12px] space-y-2 leading-relaxed">
//         <p><strong>Revenue:</strong> Sales/Revenue experienced a significant year-over-year increase of +16.7%, rising from SAR 2,257.5 Million to SAR 2,635 Million. QOQ growth was +11.5% (from SAR 2,363M).</p>
//         <p><strong>Cost of Revenue:</strong> Increased by +20.6% YOY (SAR 1,396.7M → SAR 1,684.1M) and +13.7% QOQ.</p>
//         <p><strong>Gross Profit:</strong> Grew +10.5% YOY and ****% QOQ.</p>
//         <p><strong>Operating Profit:</strong> Rose +16.6% YOY and +40.4% QOQ.</p>
//         <p><strong>Net Profit:</strong> Increased ****% YOY and +61.6% QOQ.</p>
//         <p><strong>Total Comprehensive Income:</strong> Grew ****% YOY and +42.9% QOQ.</p>
//         <p><strong>Non-Operational Costs:</strong> Switched from SAR 13.8M to 18.4M YOY and -3.4M to 18.4M QOQ (change undefined).</p>
//       </div>
//     </div>
//   );
// };

// export default ReportsDetailsPage;





// import React from "react";
// import OverviewTab from "../../components/tabs/OverviewTab/OverviewTab";
// // import StockChart from "../../components/tabs/OverviewTab/StockChart";

// const ReportsDetailsPage = () => {
//   return (
//     <div className="p-4 text-[12px] space-y-4">
//       {/* Top Summary */}
//       <div className="text-center font-semibold border border-gray-300 bg-gray-50 p-2 rounded">
//         Revenue <span className="text-green-600">+16.7%</span> (SAR +377.5 Million) To reach <strong>SAR 2,635 Million</strong> and Net Profit <span className="text-green-600">****%</span> (SAR +22.3 Million) To reach <strong>SAR 255.2 Million</strong>.
//       </div>

//       {/* Table + Chart + Overview */}
//       <div className="flex flex-col lg:flex-row gap-4">
//         {/* Table */}
//         <div className="lg:w-2/3 w-full overflow-x-auto border border-black rounded-md">
//           <table className="w-full text-center border-collapse">
//             <thead className="bg-gray-200 text-[11px]">
//               <tr>
//                 <th className="border border-black p-1">Item</th>
//                 <th className="border border-black p-1">Quarter 2025</th>
//                 <th className="border border-black p-1">Similar Quarter 2024</th>
//                 <th className="border border-black p-1">Previous Quarter 2025</th>
//                 <th className="border border-black p-1">Change YOY</th>
//                 <th className="border border-black p-1">Change QOQ</th>
//               </tr>
//             </thead>
//             <tbody className="text-[11px]">
//               {[
//                 ["Sales/Revenue", "2,635", "2,257.5", "2,363", "+16.7%", "+11.5%"],
//                 ["Cost of Revenue", "1,684.1", "1,396.7", "1,480.6", "+20.6%", "+13.7%"],
//                 ["Gross Profit", "950.9", "860.8", "882.4", "+10.5%", "****%"],
//                 ["Operating Profit", "270", "231.6", "192.3", "+16.6%", "+40.4%"],
//                 ["Net Profit (Loss)", "255.2", "232.9", "157.9", "****%", "+61.6%"],
//                 ["Total Comprehensive Income", "252.5", "244.2", "177", "****%", "+42.9%"],
//                 ["Non-Operational Costs", "18.4", "13.8", "-3.4", "+33.3%", "—"],
//               ].map((row, i) => (
//                 <tr key={i} className={i % 2 === 0 ? "bg-white" : "bg-gray-50"}>
//                   {row.map((cell, j) => (
//                     <td key={j} className="border border-black p-1">{cell}</td>
//                   ))}
//                 </tr>
//               ))}
//             </tbody>
//           </table>
//         </div>

//        {/* <div className="flex flex-col gap-4 w-full lg:w-1/3">
//   <div className="border rounded-md shadow p-4 bg-white">
//     {/* <StockChart /> 
//   </div> */}
//   <div className="border rounded-md shadow p-4 bg-white">
//     <OverviewTab />
//   </div>
// </div>

//       {/* Breakdown Text */}
//       <div className="text-[12px] space-y-2 leading-relaxed">
//         <p><strong>Revenue:</strong> Sales/Revenue experienced a significant year-over-year increase of +16.7%, rising from SAR 2,257.5 Million to SAR 2,635 Million. QOQ growth was +11.5% (from SAR 2,363M).</p>
//         <p><strong>Cost of Revenue:</strong> Increased by +20.6% YOY (SAR 1,396.7M → SAR 1,684.1M) and +13.7% QOQ.</p>
//         <p><strong>Gross Profit:</strong> Grew +10.5% YOY and ****% QOQ.</p>
//         <p><strong>Operating Profit:</strong> Rose +16.6% YOY and +40.4% QOQ.</p>
//         <p><strong>Net Profit:</strong> Increased ****% YOY and +61.6% QOQ.</p>
//         <p><strong>Total Comprehensive Income:</strong> Grew ****% YOY and +42.9% QOQ.</p>
//         <p><strong>Non-Operational Costs:</strong> Switched from SAR 13.8M to 18.4M YOY and -3.4M to 18.4M QOQ (change undefined).</p>
//       </div>
//     </div>
//   );
// };

// export default ReportsDetailsPage;






// import React from "react";
// import StockChart from "../../components/tabs/OverviewTab/StockChart";
// import StockDetailsCard from "../../components/tabs/OverviewTab/StockDetailsCard";

// const ReportsDetailsPage = () => {
//   // Mock chart data
//   const chartData = {
//     data: {
//       labels: ["Jan", "Feb", "Mar", "Apr", "May"],
//       datasets: [
//         {
//           label: "Stock Price",
//           data: [120, 130, 125, 140, 135],
//           borderColor: "#4F46E5",
//           backgroundColor: "rgba(79, 70, 229, 0.2)",
//           fill: true,
//         },
//       ],
//     },
//     options: {
//       responsive: true,
//       plugins: {
//         legend: { position: "top" },
//         title: { display: true, text: "Stock Price Trend" },
//       },
//     },
//     timeRanges: ["1D", "1W", "1M", "3M", "1Y"],
//     priceOpen: "120.00",
//     priceClose: "135.00",
//     change: "+15.00",
//     percentChange: "+12.5",
//   };

//   // Mock metrics
//   const metrics = [
//     { id: "1", label: "Market Cap", value: "SAR 15B" },
//     { id: "2", label: "PE Ratio", value: "22.5" },
//     { id: "3", label: "EPS", value: "5.32" },
//     { id: "4", label: "Dividend Yield", value: "2.4%" },
//   ];

//   return (
//     <div className="p-4 space-y-6 text-[12px]">
//       {/* Summary */}
//       <div className="text-center font-semibold">
//         Revenue +16.7% (SAR +377.5 Million) To reach SAR 2,635 Million and Net Profit ****% (SAR +22.3 Million) To reach SAR 255.2 Million.
//       </div>

//       {/* Stock Chart and Details Card */}
//       <div className="flex flex-col lg:flex-row items-center gap-6">
//         <StockChart chartData={chartData} />
//         <StockDetailsCard metrics={metrics} />
//       </div>

//       {/* Financial Table */}
//       <div className="overflow-x-auto border border-black">
//         <table className="w-full text-center border-collapse">
//           <thead className="bg-gray-200 text-[11px]">
//             <tr>
//               <th className="border border-black p-1">Item</th>
//               <th className="border border-black p-1">Quarter 2025</th>
//               <th className="border border-black p-1">Similar Quarter 2024</th>
//               <th className="border border-black p-1">Previous Quarter 2025</th>
//               <th className="border border-black p-1">Change YOY</th>
//               <th className="border border-black p-1">Change QOQ</th>
//             </tr>
//           </thead>
//           <tbody className="text-[11px]">
//             <tr>
//               <td className="border border-black p-1">Sales/Revenue</td>
//               <td className="border border-black p-1">2,635</td>
//               <td className="border border-black p-1">2,257.5</td>
//               <td className="border border-black p-1">2,363</td>
//               <td className="border border-black p-1">+16.7%</td>
//               <td className="border border-black p-1">+11.5%</td>
//             </tr>
//             <tr>
//               <td className="border border-black p-1">Cost of Revenue</td>
//               <td className="border border-black p-1">1,684.1</td>
//               <td className="border border-black p-1">1,396.7</td>
//               <td className="border border-black p-1">1,480.6</td>
//               <td className="border border-black p-1">+20.6%</td>
//               <td className="border border-black p-1">+13.7%</td>
//             </tr>
//             <tr>
//               <td className="border border-black p-1">Gross Profit</td>
//               <td className="border border-black p-1">950.9</td>
//               <td className="border border-black p-1">860.8</td>
//               <td className="border border-black p-1">882.4</td>
//               <td className="border border-black p-1">+10.5%</td>
//               <td className="border border-black p-1">****%</td>
//             </tr>
//             <tr>
//               <td className="border border-black p-1">Operating Profit</td>
//               <td className="border border-black p-1">270</td>
//               <td className="border border-black p-1">231.6</td>
//               <td className="border border-black p-1">192.3</td>
//               <td className="border border-black p-1">+16.6%</td>
//               <td className="border border-black p-1">+40.4%</td>
//             </tr>
//             <tr>
//               <td className="border border-black p-1">Net Profit (Loss)</td>
//               <td className="border border-black p-1">255.2</td>
//               <td className="border border-black p-1">232.9</td>
//               <td className="border border-black p-1">157.9</td>
//               <td className="border border-black p-1">****%</td>
//               <td className="border border-black p-1">+61.6%</td>
//             </tr>
//             <tr>
//               <td className="border border-black p-1">Total Comprehensive Income</td>
//               <td className="border border-black p-1">252.5</td>
//               <td className="border border-black p-1">244.2</td>
//               <td className="border border-black p-1">177</td>
//               <td className="border border-black p-1">****%</td>
//               <td className="border border-black p-1">+42.9%</td>
//             </tr>
//             <tr>
//               <td className="border border-black p-1">Non-Operational Costs</td>
//               <td className="border border-black p-1">18.4</td>
//               <td className="border border-black p-1">13.8</td>
//               <td className="border border-black p-1">-3.4</td>
//               <td className="border border-black p-1">+33.3%</td>
//               <td className="border border-black p-1">—</td>
//             </tr>
//           </tbody>
//         </table>
//       </div>

//       {/* Breakdown Summary */}
//       <div className="text-[12px] space-y-2">
//         <p><strong>Revenue:</strong> Sales/Revenue experienced a significant year-over-year increase of +16.7%, rising from SAR 2,257.5 Million to SAR 2,635 Million in the current quarter. On a quarter-over-quarter basis, revenue also showed a positive growth of +11.5%.</p>
//         <p><strong>Cost of Revenue:</strong> Increased +20.6% YoY and +13.7% QoQ.</p>
//         <p><strong>Gross Profit:</strong> Increased +10.5% YoY and ****% QoQ.</p>
//         <p><strong>Operating Income:</strong> Rose +16.6% YoY and +40.4% QoQ.</p>
//         <p><strong>Net Income:</strong> Gained ****% YoY and surged +61.6% QoQ.</p>
//         <p><strong>Total Comprehensive Income:</strong> Up ****% YoY and +42.9% QoQ.</p>
//         <p><strong>Non-Operational Costs:</strong> Increased, but percentage change not defined due to negative base.</p>
//       </div>
//     </div>
//   );
// };

// export default ReportsDetailsPage;



import React from "react";
import StockChart from "../../components/tabs/OverviewTab/StockChart";
import StockDetailsCard from "../../components/tabs/OverviewTab/StockDetailsCard";

const ReportsDetailsPage = () => {
  // Mock chart data
  const chartData = {
    data: {
      labels: ["Jan", "Feb", "Mar", "Apr", "May"],
      datasets: [
        {
          label: "Stock Price",
          data: [120, 130, 125, 140, 135],
          borderColor: "#4F46E5",
          backgroundColor: "rgba(79, 70, 229, 0.2)",
          fill: true,
        },
      ],
    },
    options: {
      responsive: true,
      plugins: {
        legend: { position: "top" },
        title: { display: true, text: "Stock Price Trend" },
      },
    },
    timeRanges: ["1D", "1W", "1M", "3M", "1Y"],
    priceOpen: "120.00",
    priceClose: "135.00",
    change: "+15.00",
    percentChange: "+12.5",
  };

  const metrics = [
    { id: "1", label: "Market Cap", value: "SAR 15B" },
    { id: "2", label: "PE Ratio", value: "22.5" },
    { id: "3", label: "EPS", value: "5.32" },
    { id: "4", label: "Dividend Yield", value: "2.4%" },
    
  ];

  return (
    <div className="p-3 text-[12px] space-y-6">
      {/* Summary */}
      <div className="text-center font-semibold">
        Revenue +16.7% (SAR +377.5 Million) To reach SAR 2,635 Million and Net Profit ****% (SAR +22.3 Million) To reach SAR 255.2 Million.
      </div>

      {/* Main Layout */}
      <div className="flex flex- lg:flex- gap-6 items-start">
        {/* Financial Table - Left Side */}
<div className="lg:w-4/5 overflow-x-auto border border-black">
  {/* Quarter Dropdown */}
  <div className="flex justify-end p-2 bg-gray-100 border-b border-black">
    <select className="border border-gray-400 rounded px-3 py-1 text-sm focus:outline-none">
      <option>First Quarter</option>
      <option>Second Quarter</option>
      <option>Third Quarter</option>
      <option>Fourth Quarter</option>
    </select>
  </div>
                             
          <table className="w-full text-center border-collapse">
            <thead className="bg-gray-200 text-[11px]">
              <tr>
                <th className="border border-black p-1">Item</th>
                <th className="border border-black p-1">Quarter 2025</th>
                <th className="border border-black p-1">Similar Quarter 2024</th>
                <th className="border border-black p-1">Previous Quarter 2025</th>
                <th className="border border-black p-1">Change YOY</th>
                <th className="border border-black p-1">Change QOQ</th>
              </tr>
            </thead>
            <tbody className="text-[11px]">
              <tr>
                <td className="border border-black p-1">Sales/Revenue</td>
                <td className="border border-black p-1">2,635</td>
                <td className="border border-black p-1">2,257.5</td>
                <td className="border border-black p-1">2,363</td>
                <td className="border border-black p-1">+16.7%</td>
                <td className="border border-black p-1">+11.5%</td>
              </tr>
              <tr>
                <td className="border border-black p-1">Cost of Revenue</td>
                <td className="border border-black p-1">1,684.1</td>
                <td className="border border-black p-1">1,396.7</td>
                <td className="border border-black p-1">1,480.6</td>
                <td className="border border-black p-1">+20.6%</td>
                <td className="border border-black p-1">+13.7%</td>
              </tr>
              <tr>
                <td className="border border-black p-1">Gross Profit</td>
                <td className="border border-black p-1">950.9</td>
                <td className="border border-black p-1">860.8</td>
                <td className="border border-black p-1">882.4</td>
                <td className="border border-black p-1">+10.5%</td>
                <td className="border border-black p-1">****%</td>
              </tr>
              <tr>
                <td className="border border-black p-1">Operating Profit</td>
                <td className="border border-black p-1">270</td>
                <td className="border border-black p-1">231.6</td>
                <td className="border border-black p-1">192.3</td>
                <td className="border border-black p-1">+16.6%</td>
                <td className="border border-black p-1">+40.4%</td>
              </tr>
              <tr>
                <td className="border border-black p-1">Net Profit (Loss)</td>
                <td className="border border-black p-1">255.2</td>
                <td className="border border-black p-1">232.9</td>
                <td className="border border-black p-1">157.9</td>
                <td className="border border-black p-1">****%</td>
                <td className="border border-black p-1">+61.6%</td>
              </tr>
              <tr>
                <td className="border border-black p-1">Total Comprehensive Income</td>
                <td className="border border-black p-1">252.5</td>
                <td className="border border-black p-1">244.2</td>
                <td className="border border-black p-1">177</td>
                <td className="border border-black p-1">****%</td>
                <td className="border border-black p-1">+42.9%</td>
              </tr>
              <tr>
                <td className="border border-black p-1">Non-Operational Costs</td>
                <td className="border border-black p-1">18.4</td>
                <td className="border border-black p-1">13.8</td>
                <td className="border border-black p-1">-3.4</td>
                <td className="border border-black p-1">+33.3%</td>
                <td className="border border-black p-1">—</td>
              </tr>
            </tbody>
          </table>
        </div>

       {/* Right Side: Chart above Details */}
        <div className="lg:w-300 space-y-4 flex flex-col items-center mt-6">
          <StockChart chartData={chartData} />
         <div className="lg:w-150 border-gray rounded-md shadow bg-white w-300 h-0 flex flex-col items-center mt-2 ml*2" > 
          <StockDetailsCard metrics={metrics} />
           </div>
        </div>
      </div>

      {/* Breakdown Summary */}
      <div className="lg:w-2/4 -mt-4 text-[13px] space-y-2">
        <p><strong>Revenue:</strong> Sales/Revenue experienced a significant year-over-year increase of +16.7%, rising from SAR 2,257.5 Million to SAR 2,635 Million in the current quarter. On a quarter-over-quarter basis, revenue also showed a positive growth of +11.5%.</p>
        <p><strong>Cost of Revenue:</strong> Increased +20.6% YoY and +13.7% QoQ.</p>
        <p><strong>Gross Profit:</strong> Increased +10.5% YoY and ****% QoQ.</p>
        <p><strong>Operating Income:</strong> Rose +16.6% YoY and +40.4% QoQ.</p>
        <p><strong>Net Income:</strong> Gained ****% YoY and surged +61.6% QoQ.</p>
        <p><strong>Total Comprehensive Income:</strong> Up ****% YoY and +42.9% QoQ.</p>
        <p><strong>Non-Operational Costs:</strong> Increased, but percentage change not defined due to negative base.</p>
      </div>
    </div>
  );
};

export default ReportsDetailsPage;
