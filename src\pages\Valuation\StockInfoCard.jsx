import PropTypes from "prop-types";
import { useTranslation } from "react-i18next";
import { Skeleton, Spin } from "antd";

const StockInfoCard = ({ stockData, loading = false }) => {
  const { t } = useTranslation();

  // Show "no stock selected" state
  if (stockData.noStockSelected) {
    return (
      <div className="flex items-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 shadow-sm w-full">
        <div className="grow flex items-center space-x-3">
          <div className="h-12 w-12 bg-gray-400 rounded-full flex items-center justify-center text-white font-bold text-lg">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-600 dark:text-gray-300">
              {t("valuation.selectStock") || "Select a stock to analyze"}
            </h3>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {t("valuation.searchInstruction") ||
                "Use the search bar above to find and select a stock"}
            </div>
          </div>
        </div>
        <div className="text-xs text-right text-gray-500 dark:text-gray-400 ml-auto">
          {t("valuation.readyToAnalyze") || "Ready to analyze"}
        </div>
      </div>
    );
  }

  // Show loading skeleton
  if (loading) {
    return (
      <div className="flex items-center p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm w-full">
        <div className="grow flex items-center space-x-3">
          <Skeleton.Avatar active size={48} shape="circle" />
          <div className="flex-1">
            <Skeleton.Input
              active
              size="small"
              style={{ width: 150 }}
              className="mb-2"
            />
            <div className="flex items-center gap-2">
              <Skeleton.Input active size="small" style={{ width: 80 }} />
              <Skeleton.Input active size="small" style={{ width: 60 }} />
            </div>
          </div>
        </div>
        <div className="text-right">
          <Skeleton.Input active size="small" style={{ width: 120 }} />
        </div>
      </div>
    );
  }

  // Check if data was not found
  if (stockData.dataNotFound) {
    return (
      <div className="flex items-center p-4 bg-white dark:bg-gray-800 rounded-lg border border-red-300 dark:border-red-700 shadow-sm text-gray-900 dark:text-white w-full">
        <div className="grow flex items-center space-x-3">
          <div className="h-12 w-12 bg-red-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </div>

          <div className="flex-1">
            <div className="flex flex-col sm:flex-row sm:items-center sm:flex-wrap gap-2">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {t("valuation.dataNotFound") || "Data not found"}
                {stockData.ticker && (
                  <span className="ml-1 text-gray-700 dark:text-gray-300">
                    ({stockData.ticker})
                  </span>
                )}
              </h3>

              <div className="text-sm text-red-600 dark:text-red-400">
                {t("valuation.noStockDataMessage") ||
                  "No stock data available for this symbol"}
              </div>
            </div>
          </div>
        </div>

        <div className="text-xs text-right text-gray-500 dark:text-gray-400 ml-auto">
          {stockData.timestamp}
        </div>
      </div>
    );
  }

  // Handle both API response formats and provide defaults
  const {
    name = "Unknown",
    ticker = stockData.symbol || "0000", // Use symbol as fallback
    price = stockData.stockPrice || 0,
    currency = stockData.currency || "SAR",
    change = stockData.priceChange || 0,
    changePercent = stockData.priceChange1D || 0,
    timestamp = stockData.timestamp ||
      new Date().toLocaleString("en-US", {
        month: "short",
        day: "numeric",
        year: "numeric",
        hour: "numeric",
        minute: "numeric",
        hour12: true,
      }),
  } = stockData;

  // Parse values to ensure they're numbers
  const numericPrice = typeof price === "string" ? parseFloat(price) : price;
  const numericChange =
    typeof change === "string" ? parseFloat(change) : change;

  // Handle percentage value (remove % sign if present and convert to number)
  let numericChangePercent = 0;
  if (typeof changePercent === "string") {
    numericChangePercent = parseFloat(changePercent.replace("%", ""));
  } else if (typeof changePercent === "number") {
    numericChangePercent = changePercent;
  }

  // Format the timestamp to show only date and time with AM/PM
  const formatTimestamp = (timestamp) => {
    if (!timestamp) return "";
    const date = new Date(timestamp);
    return date.toLocaleString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    });
  };

  // green with up arrow for positive changes, red with down arrow for negative changes
  const isPositive = numericChangePercent > 0;
  const changeColor = isPositive
    ? "text-green-500 dark:text-green-400"
    : "text-red-500 dark:text-red-400";
  const changeIcon = isPositive ? "▲" : "▼";

  return (
    <div className="flex items-center p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm text-gray-900 dark:text-white w-full">
      <div className="grow flex items-center space-x-3">
        <div className="h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
          {ticker.substring(0, 2)}
        </div>

        <div className="flex-1">
          <div className="flex flex-col sm:flex-row sm:items-center sm:flex-wrap gap-2">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {name}
              <span className="ml-1 text-gray-700 dark:text-gray-300">
                ({ticker})
              </span>
            </h3>

            <div className="flex items-center gap-2">
              <span className="text-lg font-medium text-gray-900 dark:text-white">
                {isNaN(numericPrice) ? "0.00" : numericPrice.toFixed(2)}{" "}
                {currency}
              </span>
              <span className={`flex items-center ${changeColor}`}>
                {changeIcon}
                {isNaN(numericChange)
                  ? "0.00"
                  : Math.abs(numericChange).toFixed(2)}
                (
                {isNaN(numericChangePercent)
                  ? "0.00"
                  : Math.abs(numericChangePercent).toFixed(2)}
                %)
              </span>
            </div>

            {/* <span className="text-xs px-2 py-1 rounded bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300">
              {t('valuation.exchange') || 'Tadawul'}
            </span> */}
          </div>
        </div>
      </div>

      <div className="text-xs text-right text-gray-500 dark:text-gray-400 ml-auto">
        {formatTimestamp(timestamp)}

        {/* <div className="mt-1">
          <span className="px-2 py-1 text-xs rounded bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300">
            {t('valuation.country') || 'Saudi Arabia'}
          </span>
        </div> */}
      </div>
    </div>
  );
};

StockInfoCard.propTypes = {
  loading: PropTypes.bool,
  stockData: PropTypes.shape({
    // Support both direct properties and API response properties
    name: PropTypes.string,
    ticker: PropTypes.string,
    symbol: PropTypes.string, // Alternative to ticker
    price: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
    stockPrice: PropTypes.oneOfType([PropTypes.number, PropTypes.string]), // Alternative to price
    currency: PropTypes.string,
    change: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
    priceChange: PropTypes.oneOfType([PropTypes.number, PropTypes.string]), // Alternative to change
    changePercent: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
    priceChange1D: PropTypes.oneOfType([PropTypes.number, PropTypes.string]), // Alternative to changePercent
    timestamp: PropTypes.string,
    noStockSelected: PropTypes.bool, // Flag for no stock selected state
    dataNotFound: PropTypes.bool, // Flag for data not found state
  }).isRequired,
};

export default StockInfoCard;
