import React from "react";
import { useNavigate } from "react-router-dom";
import { Globe } from "lucide-react";

// Single Card Component
const StockCard = ({ stock }) => {
  const navigate = useNavigate();

  const handleClick = () => {
    // navigate(/stock/${stock.id}); // Dynamic stock ID
  };

  return (
    <div
      onClick={handleClick}
      className="cursor-pointer flex items-center justify-between px-2 py-1 bg-[#e5e5e5] border border-black rounded-none w-full text-[12px] hover:bg-gray-300 transition"
    >
      {/* Left Section */}
      <div className="flex items-center space-x-2 w-[33%]">
        <Globe className="text-green-600 w-4 h-4" />
        <div>
          <div className="font-semibold text-[13px]">
            {stock.name} ({stock.id})
          </div>
          <div className="flex items-center space-x-2 text-[12px]">
            <span>{stock.price} Sar</span>
            <span className={stock.change < 0 ? "text-red-500" : "text-green-600"}>
              {stock.change > 0 ? "+" : ""}
              {stock.change} ({stock.changePercent}%)
            </span>
          </div>
        </div>
      </div>

      {/* Center Section */}
      <div className="text-center text-[12px] w-[45%]">
        {stock.summary}
      </div>

      {/* Right Section */}
      <div className="grid grid-cols-3 gap-x-4 text-[12px] text-center w-[22%]">
        <div className="flex flex-col">
          <span className="font-semibold border-b border-black">Interim</span>
          <span>{stock.interim}</span>
        </div>
        <div className="flex flex-col">
          <span className="font-semibold border-b border-black">Year</span>
          <span>{stock.year}</span>
        </div>
        <div className="flex flex-col">
          <span className="font-semibold border-b border-black">Market</span>
          <span>{stock.market}</span>
        </div>
      </div>
    </div>
  );
};

// Stock List Component
const ReportsList = () => {
  const stocks = [
    {
      id: "2222", name: "Saudi Aramco Base", price: 27.85, change: -0.15, changePercent: -0.54,
      summary: "Revenue +16.7% (SAR +377.5 Million) To reach SAR 2,635 Million and Net Profit +9.6% (SAR +22.3 Million) To reach SAR 255.2 Million.",
      interim: "3 Months", year: "2025", market: "Tasi"
    },
    {
      id: "1010", name: "Riyad Bank", price: 34.2, change: 0.20, changePercent: 0.59,
      summary: "Revenue +10.2% (SAR +120 Million), Net Profit +5.3% (SAR +40 Million).",
      interim: "3 Months", year: "2025", market: "Tasi"
    },
    {
      id: "2030", name: "SABIC", price: 87.5, change: -1.10, changePercent: -1.24,
      summary: "Revenue up by 8.5%, Net Profit stable at SAR 3.2 Billion.",
      interim: "Quarterly", year: "2025", market: "Tasi"
    },
    {
      id: "1150", name: "Alinma Bank", price: 18.3, change: 0.15, changePercent: 0.83,
      summary: "Net income increased by 12.4% to SAR 1.1 Billion.",
      interim: "3 Months", year: "2025", market: "Tasi"
    },
    {
      id: "8200", name: "Salama", price: 13.6, change: 0.05, changePercent: 0.37,
      summary: "Revenue rose 6.2% YoY to SAR 2.5 Billion.",
      interim: "3 Months", year: "2025", market: "Tasi"
    },
    {
      id: "2280", name: "Almarai", price: 52.1, change: 0.70, changePercent: 1.36,
      summary: "Almarai reports strong seasonal profits of SAR 1.3 Billion.",
      interim: "Half Year", year: "2025", market: "Tasi"
    },
    {
      id: "7030", name: "Mobile Telecomm", price: 33.5, change: -0.35, changePercent: -1.03,
      summary: "Net Profit drops by 3.2% due to higher costs.",
      interim: "Quarterly", year: "2025", market: "Tasi"
    },
    {
      id: "5110", name: "Saudi Cement", price: 44.0, change: 0.10, changePercent: 0.23,
      summary: "Cement sales strong in Q2. Revenue up 9%.",
      interim: "Quarterly", year: "2025", market: "Tasi"
    },
    {
      id: "7010", name: "STC", price: 100.4, change: 1.4, changePercent: 1.41,
      summary: "Revenue surges by 15% to SAR 16 Billion.",
      interim: "3 Months", year: "2025", market: "Tasi"
    },
    {
      id: "2240", name: "Zain KSA", price: 11.7, change: -0.1, changePercent: -0.85,
      summary: "Profit falls slightly to SAR 230 Million.",
      interim: "3 Months", year: "2025", market: "Tasi"
    }
  ];

  return (
    <div className="space-y-2">
      {stocks.map((stock) => (
        <StockCard key={stock.id} stock={stock} />
      ))}
    </div>
  );
};

export default ReportsList;